<?php

namespace App\Modules\Auth\Controllers;

use App\Core\Controller;
use App\Core\Request;
use App\Core\Response;
use App\Core\Session;
use App\Core\Validator;
use App\Modules\Auth\Models\User;
use App\Modules\Auth\Models\EmailVerification;
use App\Modules\Auth\Services\EmailService;

class VerificationController extends Controller
{
    private $userModel;
    private $emailVerification;
    private $emailService;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
        $this->emailVerification = new EmailVerification();
        $this->emailService = new EmailService();
    }

    /**
     * Show email verification notice
     */
    public function showVerificationNotice()
    {
        return $this->view('auth/verify-email-notice', [
            'title' => 'Verify Your Email - JobSpace',
            'csrf_token' => Session::generateCsrfToken()
        ]);
    }

    /**
     * Verify email using token
     */
    public function verifyEmail(string $token)
    {
        $verification = $this->emailVerification->findByToken($token);
        
        if (!$verification) {
            return $this->view('auth/verification-result', [
                'title' => 'Verification Failed - JobSpace',
                'success' => false,
                'message' => 'Invalid or expired verification link.'
            ]);
        }

        // Get user
        $user = $this->userModel->find($verification['user_id']);
        if (!$user) {
            return $this->view('auth/verification-result', [
                'title' => 'Verification Failed - JobSpace',
                'success' => false,
                'message' => 'User not found.'
            ]);
        }

        // Check if already verified
        if ($user['email_verified_at']) {
            return $this->view('auth/verification-result', [
                'title' => 'Already Verified - JobSpace',
                'success' => true,
                'message' => 'Your email is already verified. You can now login.',
                'show_login_button' => true
            ]);
        }

        // Mark email as verified
        if ($this->userModel->markEmailAsVerified($verification['user_id'])) {
            // Mark verification as used
            $this->emailVerification->markAsVerified($verification['id']);
            
            // Send welcome email
            $this->emailService->sendWelcomeEmail($user['email'], $user['first_name']);
            
            return $this->view('auth/verification-result', [
                'title' => 'Email Verified - JobSpace',
                'success' => true,
                'message' => 'Your email has been successfully verified! You can now login to your account.',
                'show_login_button' => true
            ]);
        }

        return $this->view('auth/verification-result', [
            'title' => 'Verification Failed - JobSpace',
            'success' => false,
            'message' => 'Failed to verify email. Please try again.'
        ]);
    }

    /**
     * Verify email using OTP
     */
    public function verifyOtp()
    {
        $request = new Request();
        
        // Validate CSRF token
        if (!Session::validateCsrfToken($request->post('csrf_token'))) {
            return Response::json(['error' => 'Invalid CSRF token'], 403);
        }

        $validator = new Validator($request->all());
        $validator->rules([
            'email' => 'required|email',
            'otp' => 'required|digits:6'
        ]);

        if (!$validator->validate()) {
            return Response::json(['errors' => $validator->getErrors()], 422);
        }

        $email = $request->post('email');
        $otp = $request->post('otp');

        // Find user by email
        $user = $this->userModel->findByEmail($email);
        if (!$user) {
            return Response::json(['error' => 'User not found'], 404);
        }

        // Check if already verified
        if ($user['email_verified_at']) {
            return Response::json([
                'success' => true,
                'message' => 'Email is already verified',
                'redirect' => '/login'
            ]);
        }

        // Find verification by OTP
        $verification = $this->emailVerification->findByOTP($otp, $user['id']);
        if (!$verification) {
            return Response::json(['error' => 'Invalid or expired OTP'], 400);
        }

        // Mark email as verified
        if ($this->userModel->markEmailAsVerified($user['id'])) {
            // Mark verification as used
            $this->emailVerification->markAsVerified($verification['id']);
            
            // Send welcome email
            $this->emailService->sendWelcomeEmail($user['email'], $user['first_name']);
            
            return Response::json([
                'success' => true,
                'message' => 'Email verified successfully! You can now login.',
                'redirect' => '/login'
            ]);
        }

        return Response::json(['error' => 'Failed to verify email'], 500);
    }

    /**
     * Resend verification email
     */
    public function resendVerification()
    {
        $request = new Request();
        
        // Validate CSRF token
        if (!Session::validateCsrfToken($request->post('csrf_token'))) {
            return Response::json(['error' => 'Invalid CSRF token'], 403);
        }

        $validator = new Validator($request->all());
        $validator->rules([
            'email' => 'required|email'
        ]);

        if (!$validator->validate()) {
            return Response::json(['errors' => $validator->getErrors()], 422);
        }

        $email = $request->post('email');

        // Find user by email
        $user = $this->userModel->findByEmail($email);
        if (!$user) {
            return Response::json(['error' => 'User not found'], 404);
        }

        // Check if already verified
        if ($user['email_verified_at']) {
            return Response::json([
                'success' => true,
                'message' => 'Email is already verified',
                'redirect' => '/login'
            ]);
        }

        // Check rate limiting
        $attempts = $this->emailVerification->getVerificationAttempts($user['id'], 60);
        if ($attempts >= 3) {
            return Response::json([
                'error' => 'Too many verification attempts. Please try again in 1 hour.'
            ], 429);
        }

        try {
            // Create new verification
            $verification = $this->emailVerification->createVerification($user['id']);
            
            // Send verification email
            if ($this->emailService->sendVerificationEmail($email, $verification)) {
                return Response::json([
                    'success' => true,
                    'message' => 'Verification email sent successfully! Please check your inbox.'
                ]);
            }
        } catch (\Exception $e) {
            error_log("Resend verification error: " . $e->getMessage());
        }

        return Response::json(['error' => 'Failed to send verification email'], 500);
    }

    /**
     * Clean expired verification tokens (called by cron)
     */
    public function cleanExpiredTokens()
    {
        $this->emailVerification->cleanExpiredTokens();
        return Response::json(['success' => true, 'message' => 'Expired tokens cleaned']);
    }
}
