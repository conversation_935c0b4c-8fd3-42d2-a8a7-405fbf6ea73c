<?php

namespace App\Core;

class Response
{
    private $content;
    private $statusCode;
    private $headers;

    public function __construct(string $content = '', int $statusCode = 200, array $headers = [])
    {
        $this->content = $content;
        $this->statusCode = $statusCode;
        $this->headers = $headers;
    }

    /**
     * Set response content
     */
    public function setContent(string $content): self
    {
        $this->content = $content;
        return $this;
    }

    /**
     * Get response content
     */
    public function getContent(): string
    {
        return $this->content;
    }

    /**
     * Set status code
     */
    public function setStatusCode(int $statusCode): self
    {
        $this->statusCode = $statusCode;
        return $this;
    }

    /**
     * Get status code
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Set header
     */
    public function setHeader(string $name, string $value): self
    {
        $this->headers[$name] = $value;
        return $this;
    }

    /**
     * Get header
     */
    public function getHeader(string $name): ?string
    {
        return $this->headers[$name] ?? null;
    }

    /**
     * Get all headers
     */
    public function getHeaders(): array
    {
        return $this->headers;
    }

    /**
     * Send response
     */
    public function send(): void
    {
        // Set status code
        http_response_code($this->statusCode);

        // Send headers
        foreach ($this->headers as $name => $value) {
            header($name . ': ' . $value);
        }

        // Send content
        echo $this->content;
    }

    /**
     * Create JSON response
     */
    public static function json(array $data, int $statusCode = 200): void
    {
        header('Content-Type: application/json');
        http_response_code($statusCode);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * Create redirect response
     */
    public static function redirect(string $url, int $statusCode = 302): void
    {
        header('Location: ' . $url, true, $statusCode);
        exit;
    }

    /**
     * Create view response
     */
    public static function view(string $view, array $data = [], string $layout = null): self
    {
        $viewRenderer = new ViewRenderer();
        $content = $viewRenderer->render($view, $data, $layout);
        
        return new self($content);
    }

    /**
     * Create HTML response
     */
    public static function html(string $content, int $statusCode = 200): self
    {
        return new self($content, $statusCode, ['Content-Type' => 'text/html']);
    }

    /**
     * Create text response
     */
    public static function text(string $content, int $statusCode = 200): self
    {
        return new self($content, $statusCode, ['Content-Type' => 'text/plain']);
    }

    /**
     * Create XML response
     */
    public static function xml(string $content, int $statusCode = 200): self
    {
        return new self($content, $statusCode, ['Content-Type' => 'application/xml']);
    }

    /**
     * Create download response
     */
    public static function download(string $filePath, string $fileName = null): void
    {
        if (!file_exists($filePath)) {
            http_response_code(404);
            echo 'File not found';
            exit;
        }

        $fileName = $fileName ?: basename($filePath);
        $fileSize = filesize($filePath);
        $mimeType = mime_content_type($filePath) ?: 'application/octet-stream';

        header('Content-Type: ' . $mimeType);
        header('Content-Disposition: attachment; filename="' . $fileName . '"');
        header('Content-Length: ' . $fileSize);
        header('Cache-Control: must-revalidate');
        header('Pragma: public');

        readfile($filePath);
        exit;
    }

    /**
     * Create file response (inline)
     */
    public static function file(string $filePath, string $fileName = null): void
    {
        if (!file_exists($filePath)) {
            http_response_code(404);
            echo 'File not found';
            exit;
        }

        $fileName = $fileName ?: basename($filePath);
        $mimeType = mime_content_type($filePath) ?: 'application/octet-stream';

        header('Content-Type: ' . $mimeType);
        header('Content-Disposition: inline; filename="' . $fileName . '"');
        header('Content-Length: ' . filesize($filePath));

        readfile($filePath);
        exit;
    }

    /**
     * Create error response
     */
    public static function error(int $statusCode, string $message = ''): void
    {
        http_response_code($statusCode);
        
        $statusTexts = [
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            405 => 'Method Not Allowed',
            422 => 'Unprocessable Entity',
            429 => 'Too Many Requests',
            500 => 'Internal Server Error',
            502 => 'Bad Gateway',
            503 => 'Service Unavailable'
        ];

        $statusText = $statusTexts[$statusCode] ?? 'Error';
        $message = $message ?: $statusText;

        // Check if request expects JSON
        $request = new Request();
        if ($request->expectsJson()) {
            self::json(['error' => $message, 'status' => $statusCode], $statusCode);
        } else {
            echo "<h1>{$statusCode} {$statusText}</h1><p>{$message}</p>";
        }
        exit;
    }

    /**
     * Create success response
     */
    public static function success(string $message = 'Success', array $data = []): void
    {
        self::json(array_merge(['success' => true, 'message' => $message], $data));
    }

    /**
     * Create validation error response
     */
    public static function validationError(array $errors): void
    {
        self::json(['errors' => $errors], 422);
    }

    /**
     * Set cookie
     */
    public function setCookie(string $name, string $value, int $expire = 0, string $path = '/', string $domain = '', bool $secure = false, bool $httpOnly = true): self
    {
        setcookie($name, $value, $expire, $path, $domain, $secure, $httpOnly);
        return $this;
    }

    /**
     * Delete cookie
     */
    public function deleteCookie(string $name, string $path = '/', string $domain = ''): self
    {
        setcookie($name, '', time() - 3600, $path, $domain);
        return $this;
    }

    /**
     * Check if response is successful
     */
    public function isSuccessful(): bool
    {
        return $this->statusCode >= 200 && $this->statusCode < 300;
    }

    /**
     * Check if response is redirect
     */
    public function isRedirect(): bool
    {
        return $this->statusCode >= 300 && $this->statusCode < 400;
    }

    /**
     * Check if response is client error
     */
    public function isClientError(): bool
    {
        return $this->statusCode >= 400 && $this->statusCode < 500;
    }

    /**
     * Check if response is server error
     */
    public function isServerError(): bool
    {
        return $this->statusCode >= 500;
    }
}
