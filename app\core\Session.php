<?php

namespace App\Core;

class Session
{
    private static $started = false;

    /**
     * Start session if not already started
     */
    public static function start(): void
    {
        if (!self::$started && session_status() === PHP_SESSION_NONE) {
            session_start();
            self::$started = true;
        }
    }

    /**
     * Set session value
     */
    public static function set(string $key, $value): void
    {
        self::start();
        $_SESSION[$key] = $value;
    }

    /**
     * Get session value
     */
    public static function get(string $key, $default = null)
    {
        self::start();
        return $_SESSION[$key] ?? $default;
    }

    /**
     * Check if session key exists
     */
    public static function has(string $key): bool
    {
        self::start();
        return isset($_SESSION[$key]);
    }

    /**
     * Remove session key
     */
    public static function forget(string $key): void
    {
        self::start();
        unset($_SESSION[$key]);
    }

    /**
     * Clear all session data
     */
    public static function clear(): void
    {
        self::start();
        $_SESSION = [];
    }

    /**
     * Destroy session
     */
    public static function destroy(): void
    {
        self::start();
        session_destroy();
        self::$started = false;
    }

    /**
     * Regenerate session ID
     */
    public static function regenerate(bool $deleteOld = true): void
    {
        self::start();
        session_regenerate_id($deleteOld);
    }

    /**
     * Flash data (available for next request only)
     */
    public static function flash(string $key, $value): void
    {
        self::start();
        $_SESSION['_flash'][$key] = $value;
    }

    /**
     * Get flash data
     */
    public static function getFlash(string $key, $default = null)
    {
        self::start();
        $value = $_SESSION['_flash'][$key] ?? $default;
        unset($_SESSION['_flash'][$key]);
        return $value;
    }

    /**
     * Check if flash data exists
     */
    public static function hasFlash(string $key): bool
    {
        self::start();
        return isset($_SESSION['_flash'][$key]);
    }

    /**
     * Get all flash data and clear it
     */
    public static function getAllFlash(): array
    {
        self::start();
        $flash = $_SESSION['_flash'] ?? [];
        $_SESSION['_flash'] = [];
        return $flash;
    }

    /**
     * Generate CSRF token
     */
    public static function generateCsrfToken(): string
    {
        self::start();
        $token = bin2hex(random_bytes(32));
        $_SESSION['_csrf_token'] = $token;
        return $token;
    }

    /**
     * Get CSRF token
     */
    public static function getCsrfToken(): ?string
    {
        self::start();
        return $_SESSION['_csrf_token'] ?? null;
    }

    /**
     * Validate CSRF token
     */
    public static function validateCsrfToken(string $token): bool
    {
        self::start();
        $sessionToken = $_SESSION['_csrf_token'] ?? null;
        return $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Get session ID
     */
    public static function getId(): string
    {
        self::start();
        return session_id();
    }

    /**
     * Set session name
     */
    public static function setName(string $name): void
    {
        session_name($name);
    }

    /**
     * Get session name
     */
    public static function getName(): string
    {
        return session_name();
    }

    /**
     * Set session save path
     */
    public static function setSavePath(string $path): void
    {
        session_save_path($path);
    }

    /**
     * Get session save path
     */
    public static function getSavePath(): string
    {
        return session_save_path();
    }

    /**
     * Set session cookie parameters
     */
    public static function setCookieParams(int $lifetime, string $path = '/', string $domain = '', bool $secure = false, bool $httpOnly = true): void
    {
        session_set_cookie_params($lifetime, $path, $domain, $secure, $httpOnly);
    }

    /**
     * Get session cookie parameters
     */
    public static function getCookieParams(): array
    {
        return session_get_cookie_params();
    }

    /**
     * Check if session is active
     */
    public static function isActive(): bool
    {
        return session_status() === PHP_SESSION_ACTIVE;
    }

    /**
     * Get all session data
     */
    public static function all(): array
    {
        self::start();
        return $_SESSION;
    }

    /**
     * Put data in session (alias for set)
     */
    public static function put(string $key, $value): void
    {
        self::set($key, $value);
    }

    /**
     * Pull data from session (get and remove)
     */
    public static function pull(string $key, $default = null)
    {
        $value = self::get($key, $default);
        self::forget($key);
        return $value;
    }

    /**
     * Increment session value
     */
    public static function increment(string $key, int $value = 1): int
    {
        $current = (int) self::get($key, 0);
        $new = $current + $value;
        self::set($key, $new);
        return $new;
    }

    /**
     * Decrement session value
     */
    public static function decrement(string $key, int $value = 1): int
    {
        $current = (int) self::get($key, 0);
        $new = $current - $value;
        self::set($key, $new);
        return $new;
    }

    /**
     * Keep flash data for another request
     */
    public static function reflash(): void
    {
        self::start();
        if (isset($_SESSION['_flash'])) {
            foreach ($_SESSION['_flash'] as $key => $value) {
                self::flash($key, $value);
            }
        }
    }

    /**
     * Keep specific flash data for another request
     */
    public static function keep(array $keys): void
    {
        self::start();
        foreach ($keys as $key) {
            if (isset($_SESSION['_flash'][$key])) {
                self::flash($key, $_SESSION['_flash'][$key]);
            }
        }
    }

    /**
     * Store previous URL
     */
    public static function setPreviousUrl(string $url): void
    {
        self::set('_previous_url', $url);
    }

    /**
     * Get previous URL
     */
    public static function getPreviousUrl(string $default = '/'): string
    {
        return self::get('_previous_url', $default);
    }

    /**
     * Store intended URL (for redirecting after login)
     */
    public static function setIntendedUrl(string $url): void
    {
        self::set('_intended_url', $url);
    }

    /**
     * Get intended URL
     */
    public static function getIntendedUrl(string $default = '/'): string
    {
        return self::pull('_intended_url', $default);
    }
}
