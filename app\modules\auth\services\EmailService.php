<?php

namespace App\Modules\Auth\Services;

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailService
{
    private $mailer;
    private $config;

    public function __construct()
    {
        $this->config = require_once __DIR__ . '/../config/email.php';
        $this->setupMailer();
    }

    /**
     * Setup PHPMailer configuration
     */
    private function setupMailer(): void
    {
        $this->mailer = new PHPMailer(true);

        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = $this->config['smtp']['host'];
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = $this->config['smtp']['username'];
            $this->mailer->Password = $this->config['smtp']['password'];
            $this->mailer->SMTPSecure = $this->config['smtp']['encryption'];
            $this->mailer->Port = $this->config['smtp']['port'];

            // Default sender
            $this->mailer->setFrom($this->config['from']['address'], $this->config['from']['name']);
            
            // Content settings
            $this->mailer->isHTML(true);
            $this->mailer->CharSet = 'UTF-8';
            
        } catch (Exception $e) {
            error_log("Email configuration error: " . $e->getMessage());
        }
    }

    /**
     * Send verification email
     */
    public function sendVerificationEmail(string $email, array $verification): bool
    {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email);

            $this->mailer->Subject = 'Verify Your Email Address - JobSpace';
            
            $verificationLink = $this->config['app_url'] . '/verify-email/' . $verification['token'];
            
            $this->mailer->Body = $this->getVerificationEmailTemplate($verificationLink, $verification['otp']);
            $this->mailer->AltBody = $this->getVerificationEmailText($verificationLink, $verification['otp']);

            return $this->mailer->send();
            
        } catch (Exception $e) {
            error_log("Email sending error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send password reset email
     */
    public function sendPasswordResetEmail(string $email, array $resetData): bool
    {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email);

            $this->mailer->Subject = 'Reset Your Password - JobSpace';
            
            $resetLink = $this->config['app_url'] . '/reset-password/' . $resetData['token'];
            
            $this->mailer->Body = $this->getPasswordResetEmailTemplate($resetLink);
            $this->mailer->AltBody = $this->getPasswordResetEmailText($resetLink);

            return $this->mailer->send();
            
        } catch (Exception $e) {
            error_log("Email sending error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send welcome email
     */
    public function sendWelcomeEmail(string $email, string $firstName): bool
    {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email);

            $this->mailer->Subject = 'Welcome to JobSpace!';
            
            $this->mailer->Body = $this->getWelcomeEmailTemplate($firstName);
            $this->mailer->AltBody = $this->getWelcomeEmailText($firstName);

            return $this->mailer->send();
            
        } catch (Exception $e) {
            error_log("Email sending error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get verification email HTML template
     */
    private function getVerificationEmailTemplate(string $link, string $otp): string
    {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Verify Your Email</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #4F46E5; color: white; padding: 20px; text-align: center; }
                .content { padding: 30px; background: #f9f9f9; }
                .button { display: inline-block; padding: 12px 30px; background: #4F46E5; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                .otp { font-size: 24px; font-weight: bold; color: #4F46E5; text-align: center; padding: 20px; background: white; border-radius: 5px; margin: 20px 0; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>JobSpace</h1>
                    <p>Verify Your Email Address</p>
                </div>
                <div class='content'>
                    <h2>Welcome to JobSpace!</h2>
                    <p>Thank you for registering with JobSpace. To complete your registration, please verify your email address.</p>
                    
                    <p><strong>Option 1:</strong> Click the button below to verify your email:</p>
                    <p style='text-align: center;'>
                        <a href='{$link}' class='button'>Verify Email Address</a>
                    </p>
                    
                    <p><strong>Option 2:</strong> Use this verification code:</p>
                    <div class='otp'>{$otp}</div>
                    
                    <p>This verification link and code will expire in 10 minutes for security reasons.</p>
                    
                    <p>If you didn't create an account with JobSpace, please ignore this email.</p>
                </div>
                <div class='footer'>
                    <p>&copy; 2024 JobSpace. All rights reserved.</p>
                    <p>This is an automated email, please do not reply.</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Get verification email text template
     */
    private function getVerificationEmailText(string $link, string $otp): string
    {
        return "
        JobSpace - Verify Your Email Address
        
        Welcome to JobSpace!
        
        Thank you for registering with JobSpace. To complete your registration, please verify your email address.
        
        Option 1: Click this link to verify your email:
        {$link}
        
        Option 2: Use this verification code: {$otp}
        
        This verification link and code will expire in 10 minutes for security reasons.
        
        If you didn't create an account with JobSpace, please ignore this email.
        
        © 2024 JobSpace. All rights reserved.
        This is an automated email, please do not reply.
        ";
    }

    /**
     * Get password reset email HTML template
     */
    private function getPasswordResetEmailTemplate(string $link): string
    {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Reset Your Password</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #4F46E5; color: white; padding: 20px; text-align: center; }
                .content { padding: 30px; background: #f9f9f9; }
                .button { display: inline-block; padding: 12px 30px; background: #4F46E5; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>JobSpace</h1>
                    <p>Reset Your Password</p>
                </div>
                <div class='content'>
                    <h2>Password Reset Request</h2>
                    <p>We received a request to reset your password for your JobSpace account.</p>
                    
                    <p>Click the button below to reset your password:</p>
                    <p style='text-align: center;'>
                        <a href='{$link}' class='button'>Reset Password</a>
                    </p>
                    
                    <p>This password reset link will expire in 1 hour for security reasons.</p>
                    
                    <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>
                </div>
                <div class='footer'>
                    <p>&copy; 2024 JobSpace. All rights reserved.</p>
                    <p>This is an automated email, please do not reply.</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Get password reset email text template
     */
    private function getPasswordResetEmailText(string $link): string
    {
        return "
        JobSpace - Reset Your Password
        
        Password Reset Request
        
        We received a request to reset your password for your JobSpace account.
        
        Click this link to reset your password:
        {$link}
        
        This password reset link will expire in 1 hour for security reasons.
        
        If you didn't request a password reset, please ignore this email. Your password will remain unchanged.
        
        © 2024 JobSpace. All rights reserved.
        This is an automated email, please do not reply.
        ";
    }

    /**
     * Get welcome email HTML template
     */
    private function getWelcomeEmailTemplate(string $firstName): string
    {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Welcome to JobSpace</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #4F46E5; color: white; padding: 20px; text-align: center; }
                .content { padding: 30px; background: #f9f9f9; }
                .button { display: inline-block; padding: 12px 30px; background: #4F46E5; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 14px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>JobSpace</h1>
                    <p>Welcome to the Community!</p>
                </div>
                <div class='content'>
                    <h2>Hello {$firstName}!</h2>
                    <p>Welcome to JobSpace! Your email has been successfully verified and your account is now active.</p>
                    
                    <p>You can now:</p>
                    <ul>
                        <li>Explore quizzes and earn points</li>
                        <li>Connect with other users</li>
                        <li>Browse job opportunities</li>
                        <li>Shop for products</li>
                        <li>And much more!</li>
                    </ul>
                    
                    <p style='text-align: center;'>
                        <a href='{$this->config['app_url']}/login' class='button'>Get Started</a>
                    </p>
                    
                    <p>If you have any questions, feel free to contact our support team.</p>
                </div>
                <div class='footer'>
                    <p>&copy; 2024 JobSpace. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Get welcome email text template
     */
    private function getWelcomeEmailText(string $firstName): string
    {
        return "
        JobSpace - Welcome to the Community!
        
        Hello {$firstName}!
        
        Welcome to JobSpace! Your email has been successfully verified and your account is now active.
        
        You can now:
        - Explore quizzes and earn points
        - Connect with other users
        - Browse job opportunities
        - Shop for products
        - And much more!
        
        Get started: {$this->config['app_url']}/login
        
        If you have any questions, feel free to contact our support team.
        
        © 2024 JobSpace. All rights reserved.
        ";
    }
}
