<?php

namespace App\Modules\Auth\Controllers;

use App\Core\Controller;
use App\Core\Request;
use App\Core\Response;
use App\Core\Session;
use App\Core\Validator;
use App\Modules\Auth\Models\User;
use App\Modules\Auth\Models\PasswordReset;
use App\Modules\Auth\Services\EmailService;

class PasswordResetController extends Controller
{
    private $userModel;
    private $passwordReset;
    private $emailService;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
        $this->passwordReset = new PasswordReset();
        $this->emailService = new EmailService();
    }

    /**
     * Show forgot password form
     */
    public function showForgotPassword()
    {
        return $this->view('auth/forgot-password', [
            'title' => 'Forgot Password - JobSpace',
            'csrf_token' => Session::generateCsrfToken()
        ]);
    }

    /**
     * <PERSON><PERSON> forgot password request
     */
    public function forgotPassword()
    {
        $request = new Request();
        
        // Validate CSRF token
        if (!Session::validateCsrfToken($request->post('csrf_token'))) {
            return Response::json(['error' => 'Invalid CSRF token'], 403);
        }

        $validator = new Validator($request->all());
        $validator->rules([
            'email' => 'required|email'
        ]);

        if (!$validator->validate()) {
            return Response::json(['errors' => $validator->getErrors()], 422);
        }

        $email = $request->post('email');

        // Find user by email
        $user = $this->userModel->findByEmail($email);
        if (!$user) {
            // Don't reveal if email exists or not for security
            return Response::json([
                'success' => true,
                'message' => 'If an account with that email exists, we have sent a password reset link.'
            ]);
        }

        // Check if email is verified
        if (!$user['email_verified_at']) {
            return Response::json([
                'error' => 'Please verify your email address first before resetting password.'
            ], 403);
        }

        // Check rate limiting
        $attempts = $this->passwordReset->getResetAttempts($email, 60);
        if ($attempts >= 3) {
            return Response::json([
                'error' => 'Too many password reset attempts. Please try again in 1 hour.'
            ], 429);
        }

        try {
            // Create password reset token
            $resetData = $this->passwordReset->createResetToken($email);
            
            // Send password reset email
            if ($this->emailService->sendPasswordResetEmail($email, $resetData)) {
                return Response::json([
                    'success' => true,
                    'message' => 'Password reset link has been sent to your email address.'
                ]);
            }
        } catch (\Exception $e) {
            error_log("Password reset error: " . $e->getMessage());
        }

        return Response::json(['error' => 'Failed to send password reset email'], 500);
    }

    /**
     * Show reset password form
     */
    public function showResetPassword(string $token)
    {
        $resetData = $this->passwordReset->findByToken($token);
        
        if (!$resetData) {
            return $this->view('auth/reset-password-result', [
                'title' => 'Invalid Reset Link - JobSpace',
                'success' => false,
                'message' => 'Invalid or expired password reset link.'
            ]);
        }

        return $this->view('auth/reset-password', [
            'title' => 'Reset Password - JobSpace',
            'csrf_token' => Session::generateCsrfToken(),
            'token' => $token
        ]);
    }

    /**
     * Handle password reset
     */
    public function resetPassword()
    {
        $request = new Request();
        
        // Validate CSRF token
        if (!Session::validateCsrfToken($request->post('csrf_token'))) {
            return Response::json(['error' => 'Invalid CSRF token'], 403);
        }

        $validator = new Validator($request->all());
        $validator->rules([
            'token' => 'required',
            'password' => 'required|min:8|max:255',
            'confirm_password' => 'required|same:password'
        ]);

        if (!$validator->validate()) {
            return Response::json(['errors' => $validator->getErrors()], 422);
        }

        $token = $request->post('token');
        $password = $request->post('password');

        // Find reset token
        $resetData = $this->passwordReset->findByToken($token);
        if (!$resetData) {
            return Response::json(['error' => 'Invalid or expired reset token'], 400);
        }

        // Find user by email
        $user = $this->userModel->findByEmail($resetData['email']);
        if (!$user) {
            return Response::json(['error' => 'User not found'], 404);
        }

        try {
            // Update password
            if ($this->userModel->updatePassword($user['id'], $password)) {
                // Mark reset token as used
                $this->passwordReset->markAsUsed($resetData['id']);
                
                // Clear all remember tokens for security
                $this->userModel->update($user['id'], ['remember_token' => null]);
                
                return Response::json([
                    'success' => true,
                    'message' => 'Password has been reset successfully! You can now login with your new password.',
                    'redirect' => '/login'
                ]);
            }
        } catch (\Exception $e) {
            error_log("Password update error: " . $e->getMessage());
        }

        return Response::json(['error' => 'Failed to reset password'], 500);
    }

    /**
     * Clean expired reset tokens (called by cron)
     */
    public function cleanExpiredTokens()
    {
        $this->passwordReset->cleanExpiredTokens();
        return Response::json(['success' => true, 'message' => 'Expired tokens cleaned']);
    }
}
