<?php

namespace App\Modules\Auth\Middleware;

use App\Core\Middleware;
use App\Core\Request;
use App\Core\Response;
use App\Modules\Auth\Services\AuthService;

class AuthMiddleware extends Middleware
{
    private $authService;

    public function __construct()
    {
        $this->authService = new AuthService();
    }

    /**
     * Handle authentication middleware
     */
    public function handle(Request $request, \Closure $next)
    {
        if (!$this->authService->isAuthenticated()) {
            if ($request->isAjax()) {
                return Response::json(['error' => 'Unauthenticated'], 401);
            }
            
            return redirect('/login');
        }

        return $next($request);
    }
}
