document.addEventListener('DOMContentLoaded', function() {
    const step1Form = document.getElementById('step1Form');
    const step2Form = document.getElementById('step2Form');
    const step1Indicator = document.getElementById('step1-indicator');
    const step2Indicator = document.getElementById('step2-indicator');
    const progressLine = document.getElementById('progress-line');
    const backBtn = document.getElementById('backBtn');
    const roleCards = document.querySelectorAll('.role-card');
    const roleInput = document.getElementById('role');
    const emailInput = document.getElementById('email');
    const usernameInput = document.getElementById('username');

    let currentStep = 1;
    let emailCheckTimeout;
    let usernameCheckTimeout;

    // Step 1 form submission
    step1Form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        clearErrors();
        
        const btn = document.getElementById('step1Btn');
        const btnText = document.getElementById('step1BtnText');
        const spinner = document.getElementById('step1Spinner');
        
        btn.disabled = true;
        btnText.textContent = 'Validating...';
        spinner.classList.remove('hidden');
        
        try {
            const formData = new FormData(step1Form);
            const response = await fetch('/register/step1', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                showStep2();
            } else {
                if (data.errors) {
                    showFieldErrors(data.errors);
                } else {
                    showMessage(data.error || 'Validation failed', 'error');
                }
            }
        } catch (error) {
            showMessage('Network error. Please try again.', 'error');
        } finally {
            btn.disabled = false;
            btnText.textContent = 'Continue to Step 2';
            spinner.classList.add('hidden');
        }
    });

    // Step 2 form submission
    step2Form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        clearErrors();
        
        if (!roleInput.value) {
            showMessage('Please select a role', 'error');
            return;
        }
        
        const btn = document.getElementById('step2Btn');
        const btnText = document.getElementById('step2BtnText');
        const spinner = document.getElementById('step2Spinner');
        
        btn.disabled = true;
        btnText.textContent = 'Creating Account...';
        spinner.classList.remove('hidden');
        
        try {
            const formData = new FormData(step2Form);
            const response = await fetch('/register/step2', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 2000);
            } else {
                if (data.errors) {
                    showFieldErrors(data.errors);
                } else {
                    showMessage(data.error || 'Registration failed', 'error');
                }
            }
        } catch (error) {
            showMessage('Network error. Please try again.', 'error');
        } finally {
            btn.disabled = false;
            btnText.textContent = 'Create Account';
            spinner.classList.add('hidden');
        }
    });

    // Back button
    backBtn.addEventListener('click', function() {
        showStep1();
    });

    // Role card selection
    roleCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove selection from all cards
            roleCards.forEach(c => {
                c.classList.remove('border-indigo-500', 'bg-indigo-50');
                c.classList.add('border-gray-300');
            });
            
            // Add selection to clicked card
            this.classList.remove('border-gray-300');
            this.classList.add('border-indigo-500', 'bg-indigo-50');
            
            // Set role value
            roleInput.value = this.dataset.role;
        });
    });

    // Email availability check
    emailInput.addEventListener('input', function() {
        clearTimeout(emailCheckTimeout);
        const email = this.value.trim();
        
        if (email && isValidEmail(email)) {
            emailCheckTimeout = setTimeout(() => {
                checkEmailAvailability(email);
            }, 500);
        }
    });

    // Username availability check
    usernameInput.addEventListener('input', function() {
        clearTimeout(usernameCheckTimeout);
        const username = this.value.trim();
        
        if (username && username.length >= 3) {
            usernameCheckTimeout = setTimeout(() => {
                checkUsernameAvailability(username);
            }, 500);
        }
    });

    // Password confirmation validation
    document.getElementById('confirm_password').addEventListener('input', function() {
        const password = document.getElementById('password').value;
        const confirmPassword = this.value;
        const errorElement = document.getElementById('confirm_password-error');
        
        if (confirmPassword && password !== confirmPassword) {
            errorElement.textContent = 'Passwords do not match';
            errorElement.classList.remove('hidden');
        } else {
            errorElement.classList.add('hidden');
        }
    });

    function showStep1() {
        currentStep = 1;
        step1Form.classList.remove('hidden');
        step2Form.classList.add('hidden');
        
        step1Indicator.classList.remove('bg-gray-300', 'text-gray-500');
        step1Indicator.classList.add('bg-indigo-600', 'text-white');
        step1Indicator.nextElementSibling.classList.remove('text-gray-500');
        step1Indicator.nextElementSibling.classList.add('text-gray-900');
        
        step2Indicator.classList.remove('bg-indigo-600', 'text-white');
        step2Indicator.classList.add('bg-gray-300', 'text-gray-500');
        step2Indicator.nextElementSibling.classList.remove('text-gray-900');
        step2Indicator.nextElementSibling.classList.add('text-gray-500');
        
        progressLine.classList.remove('bg-indigo-600');
        progressLine.classList.add('bg-gray-300');
    }

    function showStep2() {
        currentStep = 2;
        step1Form.classList.add('hidden');
        step2Form.classList.remove('hidden');
        
        step2Indicator.classList.remove('bg-gray-300', 'text-gray-500');
        step2Indicator.classList.add('bg-indigo-600', 'text-white');
        step2Indicator.nextElementSibling.classList.remove('text-gray-500');
        step2Indicator.nextElementSibling.classList.add('text-gray-900');
        
        progressLine.classList.remove('bg-gray-300');
        progressLine.classList.add('bg-indigo-600');
    }

    async function checkEmailAvailability(email) {
        try {
            const response = await fetch('/check-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `email=${encodeURIComponent(email)}`
            });
            
            const data = await response.json();
            const errorElement = document.getElementById('email-error');
            const successElement = document.getElementById('email-success');
            
            if (data.available) {
                errorElement.classList.add('hidden');
                successElement.textContent = data.message;
                successElement.classList.remove('hidden');
            } else {
                successElement.classList.add('hidden');
                errorElement.textContent = data.message;
                errorElement.classList.remove('hidden');
            }
        } catch (error) {
            console.error('Email check failed:', error);
        }
    }

    async function checkUsernameAvailability(username) {
        try {
            const response = await fetch('/check-username', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `username=${encodeURIComponent(username)}`
            });
            
            const data = await response.json();
            const errorElement = document.getElementById('username-error');
            const successElement = document.getElementById('username-success');
            
            if (data.available) {
                errorElement.classList.add('hidden');
                successElement.textContent = data.message;
                successElement.classList.remove('hidden');
            } else {
                successElement.classList.add('hidden');
                errorElement.textContent = data.message;
                errorElement.classList.remove('hidden');
            }
        } catch (error) {
            console.error('Username check failed:', error);
        }
    }

    function clearErrors() {
        const errorElements = document.querySelectorAll('[id$="-error"]');
        errorElements.forEach(el => {
            el.classList.add('hidden');
            el.textContent = '';
        });
        document.getElementById('registerMessage').classList.add('hidden');
    }

    function showFieldErrors(errors) {
        for (const [field, messages] of Object.entries(errors)) {
            const errorElement = document.getElementById(field + '-error');
            if (errorElement) {
                errorElement.textContent = messages[0];
                errorElement.classList.remove('hidden');
            }
        }
    }

    function showMessage(message, type) {
        const messageDiv = document.getElementById('registerMessage');
        const messageText = document.getElementById('messageText');
        const messageIcon = document.getElementById('messageIcon');
        
        messageText.textContent = message;
        messageDiv.className = `rounded-md p-4 ${type === 'success' ? 'bg-green-50' : 'bg-red-50'}`;
        messageIcon.className = `h-5 w-5 ${type === 'success' ? 'text-green-400' : 'text-red-400'}`;
        messageText.className = `text-sm font-medium ${type === 'success' ? 'text-green-800' : 'text-red-800'}`;
        messageDiv.classList.remove('hidden');
        
        // Scroll to message
        messageDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
});
