<?php

namespace App\Core;

abstract class Middleware
{
    /**
     * Handle the middleware
     */
    abstract public function handle(Request $request, \Closure $next);

    /**
     * Render a view
     */
    protected function view(string $view, array $data = [], string $layout = null): string
    {
        $viewRenderer = new ViewRenderer();
        return $viewRenderer->render($view, $data, $layout);
    }

    /**
     * Redirect to a URL
     */
    protected function redirect(string $url, int $statusCode = 302): void
    {
        Response::redirect($url, $statusCode);
    }

    /**
     * Return JSON response
     */
    protected function json(array $data, int $statusCode = 200): void
    {
        Response::json($data, $statusCode);
    }

    /**
     * Get authenticated user
     */
    protected function user(): ?array
    {
        return Session::get('auth_user');
    }

    /**
     * Check if user is authenticated
     */
    protected function isAuthenticated(): bool
    {
        return Session::has('auth_user');
    }

    /**
     * Get client IP address
     */
    protected function getClientIp(): string
    {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];

        foreach ($ipKeys as $key) {
            if (isset($_SERVER[$key]) && !empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                return trim($ips[0]);
            }
        }

        return '127.0.0.1';
    }

    /**
     * Get user agent
     */
    protected function getUserAgent(): string
    {
        return $_SERVER['HTTP_USER_AGENT'] ?? '';
    }

    /**
     * Log middleware activity
     */
    protected function log(string $message, array $context = []): void
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $this->getClientIp(),
            'user_agent' => $this->getUserAgent(),
            'message' => $message,
            'context' => $context
        ];

        error_log(json_encode($logData));
    }

    /**
     * Rate limiting check
     */
    protected function checkRateLimit(string $key, int $maxAttempts = 60, int $timeWindow = 60): bool
    {
        $cacheKey = 'rate_limit_' . $key;
        $attempts = Session::get($cacheKey, []);
        $now = time();

        // Clean old attempts
        $attempts = array_filter($attempts, function($timestamp) use ($now, $timeWindow) {
            return ($now - $timestamp) < $timeWindow;
        });

        // Check if limit exceeded
        if (count($attempts) >= $maxAttempts) {
            return false;
        }

        // Add current attempt
        $attempts[] = $now;
        Session::set($cacheKey, $attempts);

        return true;
    }

    /**
     * Block suspicious activity
     */
    protected function blockSuspiciousActivity(string $reason = 'Suspicious activity detected'): void
    {
        $this->log('Blocked suspicious activity', [
            'reason' => $reason,
            'ip' => $this->getClientIp(),
            'user_agent' => $this->getUserAgent()
        ]);

        Response::error(403, $reason);
    }

    /**
     * Validate CSRF token
     */
    protected function validateCsrf(string $token): bool
    {
        return Session::validateCsrfToken($token);
    }

    /**
     * Check if request is from allowed origin
     */
    protected function checkOrigin(array $allowedOrigins = []): bool
    {
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        
        if (empty($allowedOrigins)) {
            return true;
        }

        return in_array($origin, $allowedOrigins);
    }

    /**
     * Sanitize input
     */
    protected function sanitizeInput(string $input): string
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Check if user has permission
     */
    protected function hasPermission(string $permission): bool
    {
        $user = $this->user();
        if (!$user) {
            return false;
        }

        // Admin has all permissions
        if ($user['role'] === 'admin') {
            return true;
        }

        // Check user permissions (implement based on your permission system)
        // This is a basic implementation
        $userPermissions = Session::get('user_permissions', []);
        return in_array($permission, $userPermissions);
    }

    /**
     * Check if user has role
     */
    protected function hasRole(string $role): bool
    {
        $user = $this->user();
        return $user && $user['role'] === $role;
    }

    /**
     * Check if user has any of the given roles
     */
    protected function hasAnyRole(array $roles): bool
    {
        $user = $this->user();
        return $user && in_array($user['role'], $roles);
    }
}
