<?php

namespace App\Modules\Auth\Middleware;

use App\Core\Middleware;
use App\Core\Request;
use App\Core\Response;
use App\Modules\Auth\Services\AuthService;

class GuestMiddleware extends Middleware
{
    private $authService;

    public function __construct()
    {
        $this->authService = new AuthService();
    }

    /**
     * Handle guest middleware (redirect authenticated users)
     */
    public function handle(Request $request, \Closure $next)
    {
        if ($this->authService->isAuthenticated()) {
            $user = $this->authService->user();
            
            if ($request->isAjax()) {
                return Response::json([
                    'error' => 'Already authenticated',
                    'redirect' => $this->getRedirectUrl($user['role'])
                ], 302);
            }
            
            return redirect($this->getRedirectUrl($user['role']));
        }

        return $next($request);
    }

    /**
     * Get redirect URL based on role
     */
    private function getRedirectUrl(string $role): string
    {
        switch ($role) {
            case 'admin':
            case 'creator':
            case 'business':
                return '/dashboard';
            case 'user':
            default:
                return '/feed';
        }
    }
}
