<?php
$layout = 'public-layout';
$title = $title ?? 'Reset Password - JobSpace';
?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-auto flex justify-center">
                <img class="h-12 w-auto" src="/assets/images/logo.png" alt="JobSpace">
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Reset your password
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Enter your new password below
            </p>
        </div>
        
        <form id="resetPasswordForm" class="mt-8 space-y-6" method="POST" action="/reset-password">
            <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
            <input type="hidden" name="token" value="<?= htmlspecialchars($token) ?>">
            
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700">New Password</label>
                <input id="password" name="password" type="password" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                       placeholder="Enter new password">
                <div class="text-red-500 text-sm mt-1 hidden" id="password-error"></div>
                <div class="text-sm text-gray-500 mt-1">
                    Password must be at least 8 characters long
                </div>
            </div>

            <div>
                <label for="confirm_password" class="block text-sm font-medium text-gray-700">Confirm New Password</label>
                <input id="confirm_password" name="confirm_password" type="password" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                       placeholder="Confirm new password">
                <div class="text-red-500 text-sm mt-1 hidden" id="confirm_password-error"></div>
            </div>

            <div>
                <button type="submit" id="resetPasswordBtn" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="h-5 w-5 text-indigo-500 group-hover:text-indigo-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
                        </svg>
                    </span>
                    <span id="resetPasswordBtnText">Reset Password</span>
                    <svg id="resetPasswordSpinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>

            <!-- Error/Success Messages -->
            <div id="resetPasswordMessage" class="hidden rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg id="messageIcon" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p id="messageText" class="text-sm font-medium"></p>
                    </div>
                </div>
            </div>
        </form>

        <div class="text-center">
            <a href="/login" class="font-medium text-indigo-600 hover:text-indigo-500">
                Back to Login
            </a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('resetPasswordForm');
    const btn = document.getElementById('resetPasswordBtn');
    const btnText = document.getElementById('resetPasswordBtnText');
    const spinner = document.getElementById('resetPasswordSpinner');
    const messageDiv = document.getElementById('resetPasswordMessage');
    const messageText = document.getElementById('messageText');
    const messageIcon = document.getElementById('messageIcon');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');

    // Password confirmation validation
    confirmPasswordInput.addEventListener('input', function() {
        const password = passwordInput.value;
        const confirmPassword = this.value;
        const errorElement = document.getElementById('confirm_password-error');
        
        if (confirmPassword && password !== confirmPassword) {
            errorElement.textContent = 'Passwords do not match';
            errorElement.classList.remove('hidden');
        } else {
            errorElement.classList.add('hidden');
        }
    });

    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Clear previous errors
        clearErrors();
        
        // Validate passwords match
        if (passwordInput.value !== confirmPasswordInput.value) {
            showMessage('Passwords do not match', 'error');
            return;
        }
        
        // Show loading state
        btn.disabled = true;
        btnText.textContent = 'Resetting...';
        spinner.classList.remove('hidden');
        
        try {
            const formData = new FormData(form);
            const response = await fetch('/reset-password', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 2000);
            } else {
                if (data.errors) {
                    showFieldErrors(data.errors);
                } else {
                    showMessage(data.error || 'Failed to reset password', 'error');
                }
            }
        } catch (error) {
            showMessage('Network error. Please try again.', 'error');
        } finally {
            // Reset loading state
            btn.disabled = false;
            btnText.textContent = 'Reset Password';
            spinner.classList.add('hidden');
        }
    });

    function clearErrors() {
        const errorElements = document.querySelectorAll('[id$="-error"]');
        errorElements.forEach(el => {
            el.classList.add('hidden');
            el.textContent = '';
        });
        messageDiv.classList.add('hidden');
    }

    function showFieldErrors(errors) {
        for (const [field, messages] of Object.entries(errors)) {
            const errorElement = document.getElementById(field + '-error');
            if (errorElement) {
                errorElement.textContent = messages[0];
                errorElement.classList.remove('hidden');
            }
        }
    }

    function showMessage(message, type) {
        messageText.textContent = message;
        messageDiv.className = `rounded-md p-4 ${type === 'success' ? 'bg-green-50' : 'bg-red-50'}`;
        messageIcon.className = `h-5 w-5 ${type === 'success' ? 'text-green-400' : 'text-red-400'}`;
        messageText.className = `text-sm font-medium ${type === 'success' ? 'text-green-800' : 'text-red-800'}`;
        messageDiv.classList.remove('hidden');
    }
});
</script>
