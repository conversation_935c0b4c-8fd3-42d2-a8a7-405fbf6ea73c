<?php
$layout = 'public-layout';
$title = $title ?? 'Register - JobSpace';
?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-auto flex justify-center">
                <img class="h-12 w-auto" src="/assets/images/logo.png" alt="JobSpace">
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Create your account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Or
                <a href="/login" class="font-medium text-indigo-600 hover:text-indigo-500">
                    sign in to your existing account
                </a>
            </p>
        </div>

        <!-- Progress Steps -->
        <div class="flex items-center justify-center space-x-4 mb-8">
            <div class="flex items-center">
                <div id="step1-indicator" class="flex items-center justify-center w-8 h-8 bg-indigo-600 text-white rounded-full text-sm font-medium">
                    1
                </div>
                <span class="ml-2 text-sm font-medium text-gray-900">Personal Info</span>
            </div>
            <div class="w-16 h-0.5 bg-gray-300" id="progress-line"></div>
            <div class="flex items-center">
                <div id="step2-indicator" class="flex items-center justify-center w-8 h-8 bg-gray-300 text-gray-500 rounded-full text-sm font-medium">
                    2
                </div>
                <span class="ml-2 text-sm font-medium text-gray-500">Account Details</span>
            </div>
        </div>

        <!-- Step 1: Personal Information -->
        <form id="step1Form" class="mt-8 space-y-6">
            <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
            
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label for="first_name" class="block text-sm font-medium text-gray-700">First Name</label>
                    <input id="first_name" name="first_name" type="text" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                           placeholder="First Name">
                    <div class="text-red-500 text-sm mt-1 hidden" id="first_name-error"></div>
                </div>
                
                <div>
                    <label for="last_name" class="block text-sm font-medium text-gray-700">Last Name</label>
                    <input id="last_name" name="last_name" type="text" required 
                           class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                           placeholder="Last Name">
                    <div class="text-red-500 text-sm mt-1 hidden" id="last_name-error"></div>
                </div>
            </div>

            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                <input id="email" name="email" type="email" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                       placeholder="Email address">
                <div class="text-red-500 text-sm mt-1 hidden" id="email-error"></div>
                <div class="text-green-500 text-sm mt-1 hidden" id="email-success"></div>
            </div>

            <div>
                <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                <input id="phone" name="phone" type="tel" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                       placeholder="Phone number">
                <div class="text-red-500 text-sm mt-1 hidden" id="phone-error"></div>
            </div>

            <div>
                <label for="date_of_birth" class="block text-sm font-medium text-gray-700">Date of Birth</label>
                <input id="date_of_birth" name="date_of_birth" type="date" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <div class="text-red-500 text-sm mt-1 hidden" id="date_of_birth-error"></div>
            </div>

            <div>
                <label for="gender" class="block text-sm font-medium text-gray-700">Gender</label>
                <select id="gender" name="gender" required 
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <option value="">Select Gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                </select>
                <div class="text-red-500 text-sm mt-1 hidden" id="gender-error"></div>
            </div>

            <div>
                <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                <input id="password" name="password" type="password" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                       placeholder="Password">
                <div class="text-red-500 text-sm mt-1 hidden" id="password-error"></div>
            </div>

            <div>
                <label for="confirm_password" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                <input id="confirm_password" name="confirm_password" type="password" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                       placeholder="Confirm Password">
                <div class="text-red-500 text-sm mt-1 hidden" id="confirm_password-error"></div>
            </div>

            <div>
                <button type="submit" id="step1Btn" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span id="step1BtnText">Continue to Step 2</span>
                    <svg id="step1Spinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </form>

        <!-- Step 2: Account Details (Hidden initially) -->
        <form id="step2Form" class="mt-8 space-y-6 hidden">
            <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
            
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
                <input id="username" name="username" type="text" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                       placeholder="Username">
                <div class="text-red-500 text-sm mt-1 hidden" id="username-error"></div>
                <div class="text-green-500 text-sm mt-1 hidden" id="username-success"></div>
            </div>

            <div>
                <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                <textarea id="address" name="address" rows="3" required 
                          class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                          placeholder="Your address"></textarea>
                <div class="text-red-500 text-sm mt-1 hidden" id="address-error"></div>
            </div>

            <!-- Role Selection Cards -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">Select Your Role</label>
                <div class="grid grid-cols-2 gap-4" id="roleCards">
                    <div class="role-card cursor-pointer border-2 border-gray-300 rounded-lg p-4 hover:border-indigo-500 transition-colors" data-role="user">
                        <div class="text-center">
                            <div class="w-12 h-12 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <h3 class="text-sm font-medium text-gray-900">User</h3>
                            <p class="text-xs text-gray-500 mt-1">Browse, shop, take quizzes</p>
                        </div>
                    </div>

                    <div class="role-card cursor-pointer border-2 border-gray-300 rounded-lg p-4 hover:border-indigo-500 transition-colors" data-role="business">
                        <div class="text-center">
                            <div class="w-12 h-12 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                                </svg>
                            </div>
                            <h3 class="text-sm font-medium text-gray-900">Business</h3>
                            <p class="text-xs text-gray-500 mt-1">Run ads, hire freelancers</p>
                        </div>
                    </div>

                    <div class="role-card cursor-pointer border-2 border-gray-300 rounded-lg p-4 hover:border-indigo-500 transition-colors" data-role="creator">
                        <div class="text-center">
                            <div class="w-12 h-12 mx-auto bg-purple-100 rounded-full flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <h3 class="text-sm font-medium text-gray-900">Creator</h3>
                            <p class="text-xs text-gray-500 mt-1">Create content, sell products</p>
                        </div>
                    </div>

                    <?php if ($show_admin_role): ?>
                    <div class="role-card cursor-pointer border-2 border-gray-300 rounded-lg p-4 hover:border-indigo-500 transition-colors" data-role="admin">
                        <div class="text-center">
                            <div class="w-12 h-12 mx-auto bg-red-100 rounded-full flex items-center justify-center mb-2">
                                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                            </div>
                            <h3 class="text-sm font-medium text-gray-900">Admin</h3>
                            <p class="text-xs text-gray-500 mt-1">Manage platform</p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                <input type="hidden" id="role" name="role" required>
                <div class="text-red-500 text-sm mt-1 hidden" id="role-error"></div>
            </div>

            <div>
                <label for="profile_picture" class="block text-sm font-medium text-gray-700">Profile Picture (Optional)</label>
                <input id="profile_picture" name="profile_picture" type="file" accept="image/*" 
                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                <div class="text-red-500 text-sm mt-1 hidden" id="profile_picture-error"></div>
            </div>

            <div>
                <label for="referral_code" class="block text-sm font-medium text-gray-700">Referral Code (Optional)</label>
                <input id="referral_code" name="referral_code" type="text" 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                       placeholder="Enter referral code">
                <div class="text-red-500 text-sm mt-1 hidden" id="referral_code-error"></div>
            </div>

            <div class="flex items-center">
                <input id="terms_accepted" name="terms_accepted" type="checkbox" required 
                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                <label for="terms_accepted" class="ml-2 block text-sm text-gray-900">
                    I agree to the <a href="/terms" class="text-indigo-600 hover:text-indigo-500">Terms and Conditions</a>
                </label>
            </div>
            <div class="text-red-500 text-sm mt-1 hidden" id="terms_accepted-error"></div>

            <div class="flex space-x-4">
                <button type="button" id="backBtn" 
                        class="flex-1 py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Back to Step 1
                </button>
                
                <button type="submit" id="step2Btn" 
                        class="flex-1 py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span id="step2BtnText">Create Account</span>
                    <svg id="step2Spinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>
        </form>

        <!-- Error/Success Messages -->
        <div id="registerMessage" class="hidden rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg id="messageIcon" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p id="messageText" class="text-sm font-medium"></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="/assets/js/auth/register.js"></script>
