<?php

namespace App\Core\Security;

class Token
{
    /**
     * Generate a random token
     */
    public static function generate(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * Generate URL-safe token
     */
    public static function urlSafe(int $length = 32): string
    {
        return rtrim(strtr(base64_encode(random_bytes($length)), '+/', '-_'), '=');
    }

    /**
     * Generate numeric token
     */
    public static function numeric(int $length = 6): string
    {
        $token = '';
        for ($i = 0; $i < $length; $i++) {
            $token .= random_int(0, 9);
        }
        return $token;
    }

    /**
     * Generate alphanumeric token
     */
    public static function alphanumeric(int $length = 32): string
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $token = '';
        $max = strlen($characters) - 1;
        
        for ($i = 0; $i < $length; $i++) {
            $token .= $characters[random_int(0, $max)];
        }
        
        return $token;
    }

    /**
     * Generate token with custom charset
     */
    public static function custom(string $charset, int $length = 32): string
    {
        $token = '';
        $max = strlen($charset) - 1;
        
        for ($i = 0; $i < $length; $i++) {
            $token .= $charset[random_int(0, $max)];
        }
        
        return $token;
    }

    /**
     * Generate JWT-style token (header.payload.signature)
     */
    public static function jwt(array $payload, string $secret, string $algorithm = 'HS256'): string
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => $algorithm]);
        $payload = json_encode($payload);
        
        $headerEncoded = self::base64UrlEncode($header);
        $payloadEncoded = self::base64UrlEncode($payload);
        
        $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $secret, true);
        $signatureEncoded = self::base64UrlEncode($signature);
        
        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }

    /**
     * Verify JWT token
     */
    public static function verifyJwt(string $token, string $secret): ?array
    {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return null;
        }
        
        [$headerEncoded, $payloadEncoded, $signatureEncoded] = $parts;
        
        $signature = self::base64UrlDecode($signatureEncoded);
        $expectedSignature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $secret, true);
        
        if (!hash_equals($signature, $expectedSignature)) {
            return null;
        }
        
        $payload = json_decode(self::base64UrlDecode($payloadEncoded), true);
        
        // Check expiration
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            return null;
        }
        
        return $payload;
    }

    /**
     * Generate time-based token
     */
    public static function timeBased(int $ttl = 3600): array
    {
        $token = self::generate(32);
        $expires = time() + $ttl;
        
        return [
            'token' => $token,
            'expires' => $expires,
            'expires_at' => date('Y-m-d H:i:s', $expires)
        ];
    }

    /**
     * Verify time-based token
     */
    public static function verifyTimeBased(string $token, int $expires): bool
    {
        return time() <= $expires;
    }

    /**
     * Generate API token with metadata
     */
    public static function apiToken(array $metadata = []): array
    {
        $token = 'js_' . self::generate(40);
        
        return array_merge([
            'token' => $token,
            'created_at' => time(),
            'type' => 'api'
        ], $metadata);
    }

    /**
     * Generate session token
     */
    public static function sessionToken(): string
    {
        return 'sess_' . self::generate(32);
    }

    /**
     * Generate CSRF token
     */
    public static function csrfToken(): string
    {
        return self::generate(32);
    }

    /**
     * Generate password reset token
     */
    public static function passwordResetToken(): array
    {
        return self::timeBased(3600); // 1 hour
    }

    /**
     * Generate email verification token
     */
    public static function emailVerificationToken(): array
    {
        return self::timeBased(600); // 10 minutes
    }

    /**
     * Generate OTP (One Time Password)
     */
    public static function otp(int $length = 6): string
    {
        return self::numeric($length);
    }

    /**
     * Generate remember token
     */
    public static function rememberToken(): string
    {
        return self::generate(60);
    }

    /**
     * Base64 URL encode
     */
    private static function base64UrlEncode(string $data): string
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * Base64 URL decode
     */
    private static function base64UrlDecode(string $data): string
    {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }

    /**
     * Generate token with prefix
     */
    public static function withPrefix(string $prefix, int $length = 32): string
    {
        return $prefix . '_' . self::generate($length);
    }

    /**
     * Validate token format
     */
    public static function isValid(string $token, int $minLength = 16): bool
    {
        return strlen($token) >= $minLength && ctype_alnum(str_replace(['_', '-'], '', $token));
    }

    /**
     * Generate secure filename
     */
    public static function filename(string $extension = ''): string
    {
        $token = self::generate(16);
        return $extension ? $token . '.' . $extension : $token;
    }

    /**
     * Generate invitation token
     */
    public static function invitationToken(): array
    {
        return [
            'token' => 'inv_' . self::generate(32),
            'expires' => time() + (7 * 24 * 3600), // 7 days
            'type' => 'invitation'
        ];
    }
}
