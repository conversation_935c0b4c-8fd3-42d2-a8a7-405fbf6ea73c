<?php
$layout = 'public-layout';
$title = $title ?? 'Email Verification - JobSpace';
?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-auto flex justify-center">
                <img class="h-12 w-auto" src="/assets/images/logo.png" alt="JobSpace">
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Email Verification
            </h2>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full <?= $success ? 'bg-green-100' : 'bg-red-100' ?>">
                    <?php if ($success): ?>
                        <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    <?php else: ?>
                        <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    <?php endif; ?>
                </div>
                
                <h3 class="mt-4 text-lg font-medium <?= $success ? 'text-green-900' : 'text-red-900' ?>">
                    <?= $success ? 'Verification Successful!' : 'Verification Failed' ?>
                </h3>
                
                <p class="mt-2 text-sm <?= $success ? 'text-green-700' : 'text-red-700' ?>">
                    <?= htmlspecialchars($message) ?>
                </p>

                <?php if (isset($show_login_button) && $show_login_button): ?>
                    <div class="mt-6">
                        <a href="/login" 
                           class="w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Continue to Login
                        </a>
                    </div>
                <?php endif; ?>

                <?php if (!$success): ?>
                    <div class="mt-6 space-y-3">
                        <a href="/verify-email-notice" 
                           class="w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Try Again
                        </a>
                        
                        <a href="/register" 
                           class="w-full flex justify-center py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Register New Account
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="text-center">
            <a href="/" class="font-medium text-indigo-600 hover:text-indigo-500">
                Back to Home
            </a>
        </div>
    </div>
</div>
