<?php
$layout = 'public-layout';
$title = $title ?? 'Verify Your Email - JobSpace';
?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-auto flex justify-center">
                <img class="h-12 w-auto" src="/assets/images/logo.png" alt="JobSpace">
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Check Your Email
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                We've sent a verification link to your email address
            </p>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                    <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                </div>
                <h3 class="mt-4 text-lg font-medium text-gray-900">Email Sent!</h3>
                <p class="mt-2 text-sm text-gray-500">
                    Please check your email inbox and click the verification link to activate your account.
                </p>
                <p class="mt-2 text-sm text-gray-500">
                    You can also use the 6-digit verification code sent to your email.
                </p>
            </div>

            <!-- OTP Verification Form -->
            <div class="mt-6">
                <form id="otpForm" class="space-y-4">
                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                        <input id="email" name="email" type="email" required 
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                               placeholder="Enter your email">
                        <div class="text-red-500 text-sm mt-1 hidden" id="email-error"></div>
                    </div>

                    <div>
                        <label for="otp" class="block text-sm font-medium text-gray-700">Verification Code</label>
                        <input id="otp" name="otp" type="text" maxlength="6" pattern="[0-9]{6}" required 
                               class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm text-center text-lg tracking-widest" 
                               placeholder="000000">
                        <div class="text-red-500 text-sm mt-1 hidden" id="otp-error"></div>
                    </div>

                    <button type="submit" id="verifyBtn" 
                            class="w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="verifyBtnText">Verify Email</span>
                        <svg id="verifySpinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </form>
            </div>

            <!-- Resend Email Section -->
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="text-center">
                    <p class="text-sm text-gray-500 mb-4">
                        Didn't receive the email?
                    </p>
                    
                    <form id="resendForm" class="space-y-4">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        
                        <div>
                            <input id="resendEmail" name="email" type="email" required 
                                   class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                                   placeholder="Enter your email to resend">
                            <div class="text-red-500 text-sm mt-1 hidden" id="resend-email-error"></div>
                        </div>

                        <button type="submit" id="resendBtn" 
                                class="w-full flex justify-center py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span id="resendBtnText">Resend Verification Email</span>
                            <svg id="resendSpinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Error/Success Messages -->
            <div id="verifyMessage" class="hidden rounded-md p-4 mt-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg id="messageIcon" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p id="messageText" class="text-sm font-medium"></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center">
            <a href="/login" class="font-medium text-indigo-600 hover:text-indigo-500">
                Back to Login
            </a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const otpForm = document.getElementById('otpForm');
    const resendForm = document.getElementById('resendForm');
    const otpInput = document.getElementById('otp');

    // Auto-format OTP input
    otpInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 6) {
            value = value.slice(0, 6);
        }
        e.target.value = value;
    });

    // OTP verification
    otpForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        clearErrors();
        
        const btn = document.getElementById('verifyBtn');
        const btnText = document.getElementById('verifyBtnText');
        const spinner = document.getElementById('verifySpinner');
        
        btn.disabled = true;
        btnText.textContent = 'Verifying...';
        spinner.classList.remove('hidden');
        
        try {
            const formData = new FormData(otpForm);
            const response = await fetch('/verify-otp', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 2000);
            } else {
                if (data.errors) {
                    showFieldErrors(data.errors);
                } else {
                    showMessage(data.error || 'Verification failed', 'error');
                }
            }
        } catch (error) {
            showMessage('Network error. Please try again.', 'error');
        } finally {
            btn.disabled = false;
            btnText.textContent = 'Verify Email';
            spinner.classList.add('hidden');
        }
    });

    // Resend email
    resendForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        clearErrors();
        
        const btn = document.getElementById('resendBtn');
        const btnText = document.getElementById('resendBtnText');
        const spinner = document.getElementById('resendSpinner');
        
        btn.disabled = true;
        btnText.textContent = 'Sending...';
        spinner.classList.remove('hidden');
        
        try {
            const formData = new FormData(resendForm);
            const response = await fetch('/resend-verification', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                showMessage(data.message, 'success');
                // Copy email to OTP form
                document.getElementById('email').value = document.getElementById('resendEmail').value;
            } else {
                if (data.errors) {
                    showFieldErrors(data.errors, 'resend-');
                } else {
                    showMessage(data.error || 'Failed to resend email', 'error');
                }
            }
        } catch (error) {
            showMessage('Network error. Please try again.', 'error');
        } finally {
            btn.disabled = false;
            btnText.textContent = 'Resend Verification Email';
            spinner.classList.add('hidden');
        }
    });

    function clearErrors() {
        const errorElements = document.querySelectorAll('[id$="-error"]');
        errorElements.forEach(el => {
            el.classList.add('hidden');
            el.textContent = '';
        });
        document.getElementById('verifyMessage').classList.add('hidden');
    }

    function showFieldErrors(errors, prefix = '') {
        for (const [field, messages] of Object.entries(errors)) {
            const errorElement = document.getElementById(prefix + field + '-error');
            if (errorElement) {
                errorElement.textContent = messages[0];
                errorElement.classList.remove('hidden');
            }
        }
    }

    function showMessage(message, type) {
        const messageDiv = document.getElementById('verifyMessage');
        const messageText = document.getElementById('messageText');
        const messageIcon = document.getElementById('messageIcon');
        
        messageText.textContent = message;
        messageDiv.className = `rounded-md p-4 mt-4 ${type === 'success' ? 'bg-green-50' : 'bg-red-50'}`;
        messageIcon.className = `h-5 w-5 ${type === 'success' ? 'text-green-400' : 'text-red-400'}`;
        messageText.className = `text-sm font-medium ${type === 'success' ? 'text-green-800' : 'text-red-800'}`;
        messageDiv.classList.remove('hidden');
    }
});
</script>
