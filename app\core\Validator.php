<?php

namespace App\Core;

class Validator
{
    private $data;
    private $rules;
    private $errors;
    private $messages;

    public function __construct(array $data)
    {
        $this->data = $data;
        $this->errors = [];
        $this->messages = [
            'required' => 'The :field field is required.',
            'email' => 'The :field must be a valid email address.',
            'min' => 'The :field must be at least :min characters.',
            'max' => 'The :field may not be greater than :max characters.',
            'same' => 'The :field and :other must match.',
            'different' => 'The :field and :other must be different.',
            'in' => 'The selected :field is invalid.',
            'not_in' => 'The selected :field is invalid.',
            'numeric' => 'The :field must be a number.',
            'integer' => 'The :field must be an integer.',
            'boolean' => 'The :field field must be true or false.',
            'date' => 'The :field is not a valid date.',
            'url' => 'The :field format is invalid.',
            'alpha' => 'The :field may only contain letters.',
            'alpha_num' => 'The :field may only contain letters and numbers.',
            'alpha_dash' => 'The :field may only contain letters, numbers, dashes and underscores.',
            'regex' => 'The :field format is invalid.',
            'unique' => 'The :field has already been taken.',
            'exists' => 'The selected :field is invalid.',
            'confirmed' => 'The :field confirmation does not match.',
            'accepted' => 'The :field must be accepted.',
            'digits' => 'The :field must be :digits digits.',
            'digits_between' => 'The :field must be between :min and :max digits.',
            'file' => 'The :field must be a file.',
            'image' => 'The :field must be an image.',
            'mimes' => 'The :field must be a file of type: :values.',
            'size' => 'The :field must be :size kilobytes.',
            'between' => 'The :field must be between :min and :max.',
        ];
    }

    /**
     * Set validation rules
     */
    public function rules(array $rules): self
    {
        $this->rules = $rules;
        return $this;
    }

    /**
     * Set custom error messages
     */
    public function messages(array $messages): self
    {
        $this->messages = array_merge($this->messages, $messages);
        return $this;
    }

    /**
     * Validate the data
     */
    public function validate(): bool
    {
        $this->errors = [];

        foreach ($this->rules as $field => $rules) {
            $rulesArray = is_string($rules) ? explode('|', $rules) : $rules;
            
            foreach ($rulesArray as $rule) {
                $this->validateRule($field, $rule);
            }
        }

        return empty($this->errors);
    }

    /**
     * Get validation errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Check if validation failed
     */
    public function fails(): bool
    {
        return !$this->validate();
    }

    /**
     * Check if validation passed
     */
    public function passes(): bool
    {
        return $this->validate();
    }

    /**
     * Validate a single rule
     */
    private function validateRule(string $field, string $rule): void
    {
        $parts = explode(':', $rule, 2);
        $ruleName = $parts[0];
        $parameters = isset($parts[1]) ? explode(',', $parts[1]) : [];

        $value = $this->data[$field] ?? null;

        switch ($ruleName) {
            case 'required':
                if (empty($value) && $value !== '0') {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'min':
                $min = (int) $parameters[0];
                if (!empty($value) && strlen($value) < $min) {
                    $this->addError($field, $ruleName, ['min' => $min]);
                }
                break;

            case 'max':
                $max = (int) $parameters[0];
                if (!empty($value) && strlen($value) > $max) {
                    $this->addError($field, $ruleName, ['max' => $max]);
                }
                break;

            case 'same':
                $other = $parameters[0];
                $otherValue = $this->data[$other] ?? null;
                if ($value !== $otherValue) {
                    $this->addError($field, $ruleName, ['other' => $other]);
                }
                break;

            case 'different':
                $other = $parameters[0];
                $otherValue = $this->data[$other] ?? null;
                if ($value === $otherValue) {
                    $this->addError($field, $ruleName, ['other' => $other]);
                }
                break;

            case 'in':
                if (!empty($value) && !in_array($value, $parameters)) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'not_in':
                if (!empty($value) && in_array($value, $parameters)) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'numeric':
                if (!empty($value) && !is_numeric($value)) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'integer':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'boolean':
                if (!empty($value) && !in_array($value, [true, false, 1, 0, '1', '0', 'true', 'false'])) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'date':
                if (!empty($value) && !strtotime($value)) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'url':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'alpha':
                if (!empty($value) && !ctype_alpha($value)) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'alpha_num':
                if (!empty($value) && !ctype_alnum($value)) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'alpha_dash':
                if (!empty($value) && !preg_match('/^[a-zA-Z0-9_-]+$/', $value)) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'regex':
                $pattern = $parameters[0];
                if (!empty($value) && !preg_match($pattern, $value)) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'confirmed':
                $confirmField = $field . '_confirmation';
                $confirmValue = $this->data[$confirmField] ?? null;
                if ($value !== $confirmValue) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'accepted':
                if (!in_array($value, [true, 1, '1', 'yes', 'on', 'true'])) {
                    $this->addError($field, $ruleName);
                }
                break;

            case 'digits':
                $digits = (int) $parameters[0];
                if (!empty($value) && (!ctype_digit($value) || strlen($value) !== $digits)) {
                    $this->addError($field, $ruleName, ['digits' => $digits]);
                }
                break;

            case 'digits_between':
                $min = (int) $parameters[0];
                $max = (int) $parameters[1];
                if (!empty($value) && (!ctype_digit($value) || strlen($value) < $min || strlen($value) > $max)) {
                    $this->addError($field, $ruleName, ['min' => $min, 'max' => $max]);
                }
                break;

            case 'between':
                $min = (float) $parameters[0];
                $max = (float) $parameters[1];
                $numValue = (float) $value;
                if (!empty($value) && ($numValue < $min || $numValue > $max)) {
                    $this->addError($field, $ruleName, ['min' => $min, 'max' => $max]);
                }
                break;
        }
    }

    /**
     * Add validation error
     */
    private function addError(string $field, string $rule, array $parameters = []): void
    {
        $message = $this->messages[$rule] ?? "The {$field} field is invalid.";
        
        // Replace placeholders
        $message = str_replace(':field', $field, $message);
        foreach ($parameters as $key => $value) {
            $message = str_replace(":{$key}", $value, $message);
        }

        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        
        $this->errors[$field][] = $message;
    }

    /**
     * Get first error for a field
     */
    public function getFirstError(string $field): ?string
    {
        return $this->errors[$field][0] ?? null;
    }

    /**
     * Check if field has errors
     */
    public function hasError(string $field): bool
    {
        return isset($this->errors[$field]);
    }

    /**
     * Get errors for a specific field
     */
    public function getFieldErrors(string $field): array
    {
        return $this->errors[$field] ?? [];
    }
}
