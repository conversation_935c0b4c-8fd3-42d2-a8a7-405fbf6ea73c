<?php

use App\Core\Router;
use App\Modules\Auth\Controllers\AuthController;
use App\Modules\Auth\Controllers\VerificationController;
use App\Modules\Auth\Controllers\PasswordResetController;

// Authentication routes
Router::get('/login', [AuthController::class, 'showLogin'])->middleware(['guest']);
Router::post('/login', [AuthController::class, 'login'])->middleware(['guest', 'csrf']);

Router::get('/register', [AuthController::class, 'showRegister'])->middleware(['guest']);
Router::post('/register/step1', [AuthController::class, 'registerStep1'])->middleware(['guest', 'csrf']);
Router::post('/register/step2', [AuthController::class, 'registerStep2'])->middleware(['guest', 'csrf']);

// AJAX routes for validation
Router::post('/check-email', [AuthController::class, 'checkEmail'])->middleware(['guest']);
Router::post('/check-username', [AuthController::class, 'checkUsername'])->middleware(['guest']);

// Email verification routes
Router::get('/verify-email/{token}', [VerificationController::class, 'verifyEmail'])->middleware(['guest']);
Router::post('/verify-otp', [VerificationController::class, 'verifyOtp'])->middleware(['guest', 'csrf']);
Router::get('/verify-email-notice', [VerificationController::class, 'showVerificationNotice'])->middleware(['guest']);
Router::post('/resend-verification', [VerificationController::class, 'resendVerification'])->middleware(['guest', 'csrf']);

// Password reset routes
Router::get('/forgot-password', [PasswordResetController::class, 'showForgotPassword'])->middleware(['guest']);
Router::post('/forgot-password', [PasswordResetController::class, 'forgotPassword'])->middleware(['guest', 'csrf']);
Router::get('/reset-password/{token}', [PasswordResetController::class, 'showResetPassword'])->middleware(['guest']);
Router::post('/reset-password', [PasswordResetController::class, 'resetPassword'])->middleware(['guest', 'csrf']);

// Logout route
Router::post('/logout', [AuthController::class, 'logout'])->middleware(['auth', 'csrf']);
