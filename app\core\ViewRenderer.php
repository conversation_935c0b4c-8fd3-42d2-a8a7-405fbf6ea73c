<?php

namespace App\Core;

class ViewRenderer
{
    private $viewPaths;
    private $layoutPaths;
    private $componentPaths;
    private $cache;

    public function __construct()
    {
        $this->viewPaths = [
            'resources/views',
            'app/modules'
        ];
        
        $this->layoutPaths = [
            'resources/views/layouts',
            'app/modules/*/views/layouts'
        ];
        
        $this->componentPaths = [
            'resources/views/components',
            'app/modules/*/views/components'
        ];
        
        $this->cache = [];
    }

    /**
     * Render a view
     */
    public function render(string $view, array $data = [], string $layout = null): string
    {
        // Extract data to variables
        extract($data);
        
        // Start output buffering
        ob_start();
        
        // Include the view file
        $viewFile = $this->findViewFile($view);
        if (!$viewFile) {
            throw new \Exception("View not found: {$view}");
        }
        
        include $viewFile;
        
        // Get the content
        $content = ob_get_clean();
        
        // If layout is specified, wrap content in layout
        if ($layout) {
            $content = $this->renderLayout($layout, array_merge($data, ['content' => $content]));
        } elseif (isset($data['layout'])) {
            $content = $this->renderLayout($data['layout'], array_merge($data, ['content' => $content]));
        }
        
        return $content;
    }

    /**
     * Render a layout
     */
    public function renderLayout(string $layout, array $data = []): string
    {
        extract($data);
        
        ob_start();
        
        $layoutFile = $this->findLayoutFile($layout);
        if (!$layoutFile) {
            throw new \Exception("Layout not found: {$layout}");
        }
        
        include $layoutFile;
        
        return ob_get_clean();
    }

    /**
     * Render a component
     */
    public function renderComponent(string $component, array $data = []): string
    {
        extract($data);
        
        ob_start();
        
        $componentFile = $this->findComponentFile($component);
        if (!$componentFile) {
            throw new \Exception("Component not found: {$component}");
        }
        
        include $componentFile;
        
        return ob_get_clean();
    }

    /**
     * Include a partial view
     */
    public function partial(string $view, array $data = []): void
    {
        echo $this->render($view, $data);
    }

    /**
     * Include a component
     */
    public function component(string $component, array $data = []): void
    {
        echo $this->renderComponent($component, $data);
    }

    /**
     * Find view file
     */
    private function findViewFile(string $view): ?string
    {
        if (isset($this->cache['views'][$view])) {
            return $this->cache['views'][$view];
        }

        $viewPath = str_replace('.', '/', $view) . '.php';
        
        // Check if it's a module view (format: module/view)
        if (strpos($view, '/') !== false) {
            $parts = explode('/', $view, 2);
            $module = $parts[0];
            $viewName = $parts[1];
            
            $moduleViewPath = "app/modules/{$module}/views/" . str_replace('.', '/', $viewName) . '.php';
            if (file_exists($moduleViewPath)) {
                $this->cache['views'][$view] = $moduleViewPath;
                return $moduleViewPath;
            }
        }
        
        // Check standard view paths
        foreach ($this->viewPaths as $path) {
            $fullPath = $path . '/' . $viewPath;
            if (file_exists($fullPath)) {
                $this->cache['views'][$view] = $fullPath;
                return $fullPath;
            }
        }
        
        return null;
    }

    /**
     * Find layout file
     */
    private function findLayoutFile(string $layout): ?string
    {
        if (isset($this->cache['layouts'][$layout])) {
            return $this->cache['layouts'][$layout];
        }

        $layoutPath = $layout . '.php';
        
        // Check layout paths
        foreach ($this->layoutPaths as $path) {
            if (strpos($path, '*') !== false) {
                // Handle wildcard paths
                $basePath = str_replace('*', '', $path);
                $modules = glob(str_replace('*', '*', $basePath), GLOB_ONLYDIR);
                
                foreach ($modules as $modulePath) {
                    $fullPath = $modulePath . '/' . $layoutPath;
                    if (file_exists($fullPath)) {
                        $this->cache['layouts'][$layout] = $fullPath;
                        return $fullPath;
                    }
                }
            } else {
                $fullPath = $path . '/' . $layoutPath;
                if (file_exists($fullPath)) {
                    $this->cache['layouts'][$layout] = $fullPath;
                    return $fullPath;
                }
            }
        }
        
        return null;
    }

    /**
     * Find component file
     */
    private function findComponentFile(string $component): ?string
    {
        if (isset($this->cache['components'][$component])) {
            return $this->cache['components'][$component];
        }

        $componentPath = str_replace('.', '/', $component) . '.php';
        
        // Check component paths
        foreach ($this->componentPaths as $path) {
            if (strpos($path, '*') !== false) {
                // Handle wildcard paths
                $basePath = str_replace('*', '', $path);
                $modules = glob(str_replace('*', '*', $basePath), GLOB_ONLYDIR);
                
                foreach ($modules as $modulePath) {
                    $fullPath = $modulePath . '/' . $componentPath;
                    if (file_exists($fullPath)) {
                        $this->cache['components'][$component] = $fullPath;
                        return $fullPath;
                    }
                }
            } else {
                $fullPath = $path . '/' . $componentPath;
                if (file_exists($fullPath)) {
                    $this->cache['components'][$component] = $fullPath;
                    return $fullPath;
                }
            }
        }
        
        return null;
    }

    /**
     * Escape HTML
     */
    public function escape(string $value): string
    {
        return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Generate URL
     */
    public function url(string $path = ''): string
    {
        $baseUrl = $_ENV['APP_URL'] ?? 'http://localhost';
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }

    /**
     * Generate asset URL
     */
    public function asset(string $path): string
    {
        return $this->url('assets/' . ltrim($path, '/'));
    }

    /**
     * Get old input value
     */
    public function old(string $key, $default = null)
    {
        return Session::getFlash('old_input')[$key] ?? $default;
    }

    /**
     * Get flash message
     */
    public function flash(string $key, $default = null)
    {
        return Session::getFlash($key, $default);
    }

    /**
     * Get CSRF token
     */
    public function csrf(): string
    {
        return Session::getCsrfToken() ?? Session::generateCsrfToken();
    }

    /**
     * Get authenticated user
     */
    public function user(): ?array
    {
        return Session::get('auth_user');
    }

    /**
     * Check if user is authenticated
     */
    public function auth(): bool
    {
        return Session::has('auth_user');
    }

    /**
     * Check if user is guest
     */
    public function guest(): bool
    {
        return !$this->auth();
    }

    /**
     * Format date
     */
    public function date(string $date, string $format = 'Y-m-d H:i:s'): string
    {
        return date($format, strtotime($date));
    }

    /**
     * Truncate text
     */
    public function truncate(string $text, int $length = 100, string $suffix = '...'): string
    {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return substr($text, 0, $length) . $suffix;
    }

    /**
     * Add view path
     */
    public function addViewPath(string $path): void
    {
        $this->viewPaths[] = $path;
    }

    /**
     * Add layout path
     */
    public function addLayoutPath(string $path): void
    {
        $this->layoutPaths[] = $path;
    }

    /**
     * Add component path
     */
    public function addComponentPath(string $path): void
    {
        $this->componentPaths[] = $path;
    }

    /**
     * Clear cache
     */
    public function clearCache(): void
    {
        $this->cache = [];
    }
}
