<?php

namespace App\Core;

abstract class Controller
{
    protected $request;
    protected $response;
    protected $session;

    public function __construct()
    {
        $this->request = new Request();
        $this->response = new Response();
        $this->session = new Session();
    }

    /**
     * Render a view
     */
    protected function view(string $view, array $data = [], string $layout = null): string
    {
        $viewRenderer = new ViewRenderer();
        return $viewRenderer->render($view, $data, $layout);
    }

    /**
     * Redirect to a URL
     */
    protected function redirect(string $url, int $statusCode = 302): void
    {
        Response::redirect($url, $statusCode);
    }

    /**
     * Return JSON response
     */
    protected function json(array $data, int $statusCode = 200): void
    {
        Response::json($data, $statusCode);
    }

    /**
     * Get authenticated user
     */
    protected function user(): ?array
    {
        return Session::get('auth_user');
    }

    /**
     * Check if user is authenticated
     */
    protected function isAuthenticated(): bool
    {
        return Session::has('auth_user');
    }

    /**
     * Validate CSRF token
     */
    protected function validateCsrf(string $token): bool
    {
        return Session::validateCsrfToken($token);
    }

    /**
     * Get request input
     */
    protected function input(string $key, $default = null)
    {
        return $this->request->input($key, $default);
    }

    /**
     * Get all request input
     */
    protected function all(): array
    {
        return $this->request->all();
    }

    /**
     * Validate request data
     */
    protected function validate(array $rules): array
    {
        $validator = new Validator($this->request->all());
        $validator->rules($rules);
        
        if (!$validator->validate()) {
            if ($this->request->isAjax()) {
                Response::json(['errors' => $validator->getErrors()], 422);
            } else {
                Session::flash('errors', $validator->getErrors());
                $this->redirect($_SERVER['HTTP_REFERER'] ?? '/');
            }
        }
        
        return $this->request->all();
    }

    /**
     * Flash message to session
     */
    protected function flash(string $key, $value): void
    {
        Session::flash($key, $value);
    }

    /**
     * Get old input value
     */
    protected function old(string $key, $default = null)
    {
        return Session::getFlash('old_input')[$key] ?? $default;
    }

    /**
     * Set old input values
     */
    protected function withInput(): void
    {
        Session::flash('old_input', $this->request->all());
    }
}
