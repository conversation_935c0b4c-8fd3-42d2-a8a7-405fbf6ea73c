<?php

namespace App\Core\Security;

class Hash
{
    /**
     * Hash a password
     */
    public static function make(string $value, array $options = []): string
    {
        $cost = $options['cost'] ?? 12;
        
        return password_hash($value, PASSWORD_ARGON2ID, [
            'memory_cost' => $options['memory'] ?? 65536, // 64 MB
            'time_cost' => $options['time'] ?? 4,
            'threads' => $options['threads'] ?? 3,
        ]);
    }

    /**
     * Verify a password against a hash
     */
    public static function verify(string $value, string $hash): bool
    {
        return password_verify($value, $hash);
    }

    /**
     * Check if hash needs rehashing
     */
    public static function needsRehash(string $hash, array $options = []): bool
    {
        $cost = $options['cost'] ?? 12;
        
        return password_needs_rehash($hash, PASSWORD_ARGON2ID, [
            'memory_cost' => $options['memory'] ?? 65536,
            'time_cost' => $options['time'] ?? 4,
            'threads' => $options['threads'] ?? 3,
        ]);
    }

    /**
     * Generate random string
     */
    public static function random(int $length = 32): string
    {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * Generate secure random bytes
     */
    public static function randomBytes(int $length = 32): string
    {
        return random_bytes($length);
    }

    /**
     * Hash data using SHA-256
     */
    public static function sha256(string $data): string
    {
        return hash('sha256', $data);
    }

    /**
     * Hash data using SHA-512
     */
    public static function sha512(string $data): string
    {
        return hash('sha512', $data);
    }

    /**
     * Generate HMAC
     */
    public static function hmac(string $data, string $key, string $algorithm = 'sha256'): string
    {
        return hash_hmac($algorithm, $data, $key);
    }

    /**
     * Verify HMAC
     */
    public static function verifyHmac(string $data, string $key, string $hash, string $algorithm = 'sha256'): bool
    {
        $calculated = self::hmac($data, $key, $algorithm);
        return hash_equals($hash, $calculated);
    }

    /**
     * Generate UUID v4
     */
    public static function uuid(): string
    {
        $data = random_bytes(16);
        $data[6] = chr(ord($data[6]) & 0x0f | 0x40); // set version to 0100
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80); // set bits 6-7 to 10
        
        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }

    /**
     * Generate cryptographically secure token
     */
    public static function token(int $length = 40): string
    {
        return rtrim(strtr(base64_encode(random_bytes($length)), '+/', '-_'), '=');
    }

    /**
     * Constant time string comparison
     */
    public static function equals(string $known, string $user): bool
    {
        return hash_equals($known, $user);
    }

    /**
     * Generate API key
     */
    public static function apiKey(string $prefix = '', int $length = 32): string
    {
        $key = self::random($length);
        return $prefix ? $prefix . '_' . $key : $key;
    }

    /**
     * Hash file contents
     */
    public static function file(string $filepath, string $algorithm = 'sha256'): string
    {
        return hash_file($algorithm, $filepath);
    }

    /**
     * Generate checksum
     */
    public static function checksum(string $data): string
    {
        return md5($data);
    }

    /**
     * Verify checksum
     */
    public static function verifyChecksum(string $data, string $checksum): bool
    {
        return hash_equals(md5($data), $checksum);
    }
}
