<?php

namespace App\Modules\Auth\Models;

use App\Core\Model;
use App\Core\Database;
use App\Core\Security\Hash;
use App\Core\Security\Token;

class User extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $fillable = [
        'first_name', 'last_name', 'username', 'email', 'phone', 
        'date_of_birth', 'gender', 'password', 'role', 'profile_picture',
        'address', 'referral_code', 'referred_by', 'status'
    ];
    
    protected $hidden = [
        'password', 'remember_token', 'two_factor_secret', 'two_factor_recovery_codes'
    ];
    
    protected $casts = [
        'email_verified_at' => 'datetime',
        'phone_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'last_activity_at' => 'datetime',
        'locked_until' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Create a new user
     */
    public function create(array $data): ?int
    {
        // Hash password
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }
        
        // Generate referral code
        if (!isset($data['referral_code'])) {
            $data['referral_code'] = $this->generateReferralCode();
        }
        
        // Set default status
        if (!isset($data['status'])) {
            $data['status'] = 'pending';
        }
        
        return parent::create($data);
    }

    /**
     * Find user by email
     */
    public function findByEmail(string $email): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE email = ? LIMIT 1";
        $result = $this->db->query($sql, [$email]);
        return $result ? $result[0] : null;
    }

    /**
     * Find user by username
     */
    public function findByUsername(string $username): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE username = ? LIMIT 1";
        $result = $this->db->query($sql, [$username]);
        return $result ? $result[0] : null;
    }

    /**
     * Find user by referral code
     */
    public function findByReferralCode(string $code): ?array
    {
        $sql = "SELECT * FROM {$this->table} WHERE referral_code = ? LIMIT 1";
        $result = $this->db->query($sql, [$code]);
        return $result ? $result[0] : null;
    }

    /**
     * Verify user password
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return Hash::verify($password, $hash);
    }

    /**
     * Update password
     */
    public function updatePassword(int $userId, string $newPassword): bool
    {
        $hashedPassword = Hash::make($newPassword);
        $sql = "UPDATE {$this->table} SET password = ?, updated_at = NOW() WHERE id = ?";
        return $this->db->execute($sql, [$hashedPassword, $userId]);
    }

    /**
     * Mark email as verified
     */
    public function markEmailAsVerified(int $userId): bool
    {
        $sql = "UPDATE {$this->table} SET email_verified_at = NOW(), status = 'active', updated_at = NOW() WHERE id = ?";
        return $this->db->execute($sql, [$userId]);
    }

    /**
     * Check if email exists
     */
    public function emailExists(string $email, ?int $excludeId = null): bool
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE email = ?";
        $params = [$email];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->query($sql, $params);
        return $result[0]['count'] > 0;
    }

    /**
     * Check if username exists
     */
    public function usernameExists(string $username, ?int $excludeId = null): bool
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE username = ?";
        $params = [$username];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $result = $this->db->query($sql, $params);
        return $result[0]['count'] > 0;
    }

    /**
     * Update login attempts
     */
    public function updateLoginAttempts(string $email, bool $success = false): bool
    {
        if ($success) {
            $sql = "UPDATE {$this->table} SET login_attempts = 0, locked_until = NULL, last_login_at = NOW() WHERE email = ?";
        } else {
            $sql = "UPDATE {$this->table} SET login_attempts = login_attempts + 1 WHERE email = ?";
        }
        
        return $this->db->execute($sql, [$email]);
    }

    /**
     * Lock user account
     */
    public function lockAccount(string $email, int $minutes = 15): bool
    {
        $sql = "UPDATE {$this->table} SET locked_until = DATE_ADD(NOW(), INTERVAL ? MINUTE) WHERE email = ?";
        return $this->db->execute($sql, [$minutes, $email]);
    }

    /**
     * Check if account is locked
     */
    public function isAccountLocked(string $email): bool
    {
        $sql = "SELECT locked_until FROM {$this->table} WHERE email = ? AND locked_until > NOW()";
        $result = $this->db->query($sql, [$email]);
        return !empty($result);
    }

    /**
     * Update last activity
     */
    public function updateLastActivity(int $userId): bool
    {
        $sql = "UPDATE {$this->table} SET last_activity_at = NOW() WHERE id = ?";
        return $this->db->execute($sql, [$userId]);
    }

    /**
     * Generate unique referral code
     */
    private function generateReferralCode(): string
    {
        do {
            $code = strtoupper(substr(md5(uniqid(rand(), true)), 0, 8));
        } while ($this->findByReferralCode($code));
        
        return $code;
    }

    /**
     * Get user statistics
     */
    public function getUserStats(int $userId): array
    {
        $sql = "SELECT 
                    COUNT(CASE WHEN referred_by = ? THEN 1 END) as referrals_count,
                    (SELECT COUNT(*) FROM user_sessions WHERE user_id = ?) as active_sessions
                FROM {$this->table}";
        
        $result = $this->db->query($sql, [$userId, $userId]);
        return $result[0] ?? [];
    }

    /**
     * Check if admin exists
     */
    public function adminExists(): bool
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE role = 'admin'";
        $result = $this->db->query($sql);
        return $result[0]['count'] > 0;
    }

    /**
     * Get users by role
     */
    public function getUsersByRole(string $role, int $limit = 10, int $offset = 0): array
    {
        $sql = "SELECT * FROM {$this->table} WHERE role = ? ORDER BY created_at DESC LIMIT ? OFFSET ?";
        return $this->db->query($sql, [$role, $limit, $offset]);
    }

    /**
     * Search users
     */
    public function searchUsers(string $query, int $limit = 10): array
    {
        $searchTerm = "%{$query}%";
        $sql = "SELECT id, first_name, last_name, username, email, role, profile_picture 
                FROM {$this->table} 
                WHERE (first_name LIKE ? OR last_name LIKE ? OR username LIKE ? OR email LIKE ?) 
                AND status = 'active'
                ORDER BY first_name, last_name 
                LIMIT ?";
        
        return $this->db->query($sql, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $limit]);
    }
}
