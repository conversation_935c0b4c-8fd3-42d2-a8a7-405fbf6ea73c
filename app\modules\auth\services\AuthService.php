<?php

namespace App\Modules\Auth\Services;

use App\Core\Session;
use App\Core\Security\Hash;
use App\Modules\Auth\Models\User;

class AuthService
{
    private $userModel;
    private $sessionKey = 'auth_user';
    private $rememberKey = 'remember_token';

    public function __construct()
    {
        $this->userModel = new User();
    }

    /**
     * Login user
     */
    public function login(array $user, bool $remember = false): bool
    {
        // Set session
        Session::set($this->sessionKey, [
            'id' => $user['id'],
            'email' => $user['email'],
            'username' => $user['username'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'role' => $user['role'],
            'profile_picture' => $user['profile_picture'],
            'logged_in_at' => time()
        ]);

        // Set remember me cookie if requested
        if ($remember) {
            $this->setRememberToken($user['id']);
        }

        // Update last login
        $this->userModel->updateLastActivity($user['id']);

        return true;
    }

    /**
     * Logout user
     */
    public function logout(): bool
    {
        // Clear remember token from database
        $user = $this->user();
        if ($user) {
            $this->clearRememberToken($user['id']);
        }

        // Clear session
        Session::forget($this->sessionKey);
        
        // Clear remember cookie
        if (isset($_COOKIE[$this->rememberKey])) {
            setcookie($this->rememberKey, '', time() - 3600, '/', '', true, true);
        }

        return true;
    }

    /**
     * Check if user is authenticated
     */
    public function isAuthenticated(): bool
    {
        // Check session first
        if (Session::has($this->sessionKey)) {
            return true;
        }

        // Check remember token
        if (isset($_COOKIE[$this->rememberKey])) {
            return $this->loginFromRememberToken($_COOKIE[$this->rememberKey]);
        }

        return false;
    }

    /**
     * Get current authenticated user
     */
    public function user(): ?array
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return Session::get($this->sessionKey);
    }

    /**
     * Get user ID
     */
    public function id(): ?int
    {
        $user = $this->user();
        return $user ? $user['id'] : null;
    }

    /**
     * Check if user has role
     */
    public function hasRole(string $role): bool
    {
        $user = $this->user();
        return $user && $user['role'] === $role;
    }

    /**
     * Check if user has any of the given roles
     */
    public function hasAnyRole(array $roles): bool
    {
        $user = $this->user();
        return $user && in_array($user['role'], $roles);
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Check if user is creator
     */
    public function isCreator(): bool
    {
        return $this->hasRole('creator');
    }

    /**
     * Check if user is business
     */
    public function isBusiness(): bool
    {
        return $this->hasRole('business');
    }

    /**
     * Check if user is regular user
     */
    public function isUser(): bool
    {
        return $this->hasRole('user');
    }

    /**
     * Refresh user data in session
     */
    public function refresh(): bool
    {
        $user = $this->user();
        if (!$user) {
            return false;
        }

        $freshUser = $this->userModel->find($user['id']);
        if (!$freshUser) {
            $this->logout();
            return false;
        }

        // Update session with fresh data
        Session::set($this->sessionKey, [
            'id' => $freshUser['id'],
            'email' => $freshUser['email'],
            'username' => $freshUser['username'],
            'first_name' => $freshUser['first_name'],
            'last_name' => $freshUser['last_name'],
            'role' => $freshUser['role'],
            'profile_picture' => $freshUser['profile_picture'],
            'logged_in_at' => $user['logged_in_at']
        ]);

        return true;
    }

    /**
     * Set remember token
     */
    private function setRememberToken(int $userId): void
    {
        $token = Hash::random(60);
        $hashedToken = Hash::make($token);

        // Save hashed token to database
        $this->userModel->update($userId, ['remember_token' => $hashedToken]);

        // Set cookie with plain token
        setcookie(
            $this->rememberKey,
            $token,
            time() + (30 * 24 * 60 * 60), // 30 days
            '/',
            '',
            true, // secure
            true  // httponly
        );
    }

    /**
     * Clear remember token
     */
    private function clearRememberToken(int $userId): void
    {
        $this->userModel->update($userId, ['remember_token' => null]);
    }

    /**
     * Login from remember token
     */
    private function loginFromRememberToken(string $token): bool
    {
        $users = $this->userModel->all();
        
        foreach ($users as $user) {
            if ($user['remember_token'] && Hash::verify($token, $user['remember_token'])) {
                // Check if user is still active
                if ($user['status'] !== 'active') {
                    return false;
                }

                // Login user
                $this->login($user, true);
                return true;
            }
        }

        return false;
    }

    /**
     * Attempt login with credentials
     */
    public function attempt(array $credentials, bool $remember = false): bool
    {
        $email = $credentials['email'] ?? '';
        $password = $credentials['password'] ?? '';

        if (!$email || !$password) {
            return false;
        }

        $user = $this->userModel->findByEmail($email);
        
        if (!$user || !$this->userModel->verifyPassword($password, $user['password'])) {
            return false;
        }

        // Check if email is verified
        if (!$user['email_verified_at']) {
            return false;
        }

        // Check if account is active
        if ($user['status'] !== 'active') {
            return false;
        }

        return $this->login($user, $remember);
    }

    /**
     * Get user's full name
     */
    public function getFullName(): string
    {
        $user = $this->user();
        if (!$user) {
            return '';
        }

        return trim($user['first_name'] . ' ' . $user['last_name']);
    }

    /**
     * Get user's avatar URL
     */
    public function getAvatarUrl(): string
    {
        $user = $this->user();
        if (!$user || !$user['profile_picture']) {
            return '/assets/images/default-avatar.png';
        }

        return '/uploads/profiles/' . $user['profile_picture'];
    }
}
