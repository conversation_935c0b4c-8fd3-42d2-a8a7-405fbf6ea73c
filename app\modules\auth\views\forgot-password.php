<?php
$layout = 'public-layout';
$title = $title ?? 'Forgot Password - JobSpace';
?>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <div class="mx-auto h-12 w-auto flex justify-center">
                <img class="h-12 w-auto" src="/assets/images/logo.png" alt="JobSpace">
            </div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Forgot your password?
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Enter your email address and we'll send you a link to reset your password.
            </p>
        </div>
        
        <form id="forgotPasswordForm" class="mt-8 space-y-6" method="POST" action="/forgot-password">
            <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
            
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
                <input id="email" name="email" type="email" autocomplete="email" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" 
                       placeholder="Enter your email address">
                <div class="text-red-500 text-sm mt-1 hidden" id="email-error"></div>
            </div>

            <div>
                <button type="submit" id="forgotPasswordBtn" 
                        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                        <svg class="h-5 w-5 text-indigo-500 group-hover:text-indigo-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                        </svg>
                    </span>
                    <span id="forgotPasswordBtnText">Send Reset Link</span>
                    <svg id="forgotPasswordSpinner" class="hidden animate-spin -mr-1 ml-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </button>
            </div>

            <!-- Error/Success Messages -->
            <div id="forgotPasswordMessage" class="hidden rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg id="messageIcon" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p id="messageText" class="text-sm font-medium"></p>
                    </div>
                </div>
            </div>
        </form>

        <div class="text-center">
            <a href="/login" class="font-medium text-indigo-600 hover:text-indigo-500">
                Back to Login
            </a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('forgotPasswordForm');
    const btn = document.getElementById('forgotPasswordBtn');
    const btnText = document.getElementById('forgotPasswordBtnText');
    const spinner = document.getElementById('forgotPasswordSpinner');
    const messageDiv = document.getElementById('forgotPasswordMessage');
    const messageText = document.getElementById('messageText');
    const messageIcon = document.getElementById('messageIcon');

    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Clear previous errors
        clearErrors();
        
        // Show loading state
        btn.disabled = true;
        btnText.textContent = 'Sending...';
        spinner.classList.remove('hidden');
        
        try {
            const formData = new FormData(form);
            const response = await fetch('/forgot-password', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                showMessage(data.message, 'success');
                form.reset();
            } else {
                if (data.errors) {
                    showFieldErrors(data.errors);
                } else {
                    showMessage(data.error || 'Failed to send reset link', 'error');
                }
            }
        } catch (error) {
            showMessage('Network error. Please try again.', 'error');
        } finally {
            // Reset loading state
            btn.disabled = false;
            btnText.textContent = 'Send Reset Link';
            spinner.classList.add('hidden');
        }
    });

    function clearErrors() {
        const errorElements = document.querySelectorAll('[id$="-error"]');
        errorElements.forEach(el => {
            el.classList.add('hidden');
            el.textContent = '';
        });
        messageDiv.classList.add('hidden');
    }

    function showFieldErrors(errors) {
        for (const [field, messages] of Object.entries(errors)) {
            const errorElement = document.getElementById(field + '-error');
            if (errorElement) {
                errorElement.textContent = messages[0];
                errorElement.classList.remove('hidden');
            }
        }
    }

    function showMessage(message, type) {
        messageText.textContent = message;
        messageDiv.className = `rounded-md p-4 ${type === 'success' ? 'bg-green-50' : 'bg-red-50'}`;
        messageIcon.className = `h-5 w-5 ${type === 'success' ? 'text-green-400' : 'text-red-400'}`;
        messageText.className = `text-sm font-medium ${type === 'success' ? 'text-green-800' : 'text-red-800'}`;
        messageDiv.classList.remove('hidden');
    }
});
</script>
