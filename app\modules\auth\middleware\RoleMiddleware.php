<?php

namespace App\Modules\Auth\Middleware;

use App\Core\Middleware;
use App\Core\Request;
use App\Core\Response;
use App\Modules\Auth\Services\AuthService;

class RoleMiddleware extends Middleware
{
    private $authService;

    public function __construct()
    {
        $this->authService = new AuthService();
    }

    /**
     * Handle role-based access control
     */
    public function handle(Request $request, \Closure $next, ...$roles)
    {
        if (!$this->authService->isAuthenticated()) {
            if ($request->isAjax()) {
                return Response::json(['error' => 'Unauthenticated'], 401);
            }
            
            return redirect('/login');
        }

        $user = $this->authService->user();
        
        if (!in_array($user['role'], $roles)) {
            if ($request->isAjax()) {
                return Response::json(['error' => 'Insufficient permissions'], 403);
            }
            
            return $this->view('errors/403', [
                'title' => 'Access Denied',
                'message' => 'You do not have permission to access this resource.'
            ]);
        }

        return $next($request);
    }
}
