<?php

namespace App\Modules\Auth\Controllers;

use App\Core\Controller;
use App\Core\Request;
use App\Core\Response;
use App\Core\Session;
use App\Core\Validator;
use App\Modules\Auth\Models\User;
use App\Modules\Auth\Models\EmailVerification;
use App\Modules\Auth\Models\PasswordReset;
use App\Modules\Auth\Services\AuthService;
use App\Modules\Auth\Services\EmailService;

class AuthController extends Controller
{
    private $userModel;
    private $emailVerification;
    private $passwordReset;
    private $authService;
    private $emailService;

    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
        $this->emailVerification = new EmailVerification();
        $this->passwordReset = new PasswordReset();
        $this->authService = new AuthService();
        $this->emailService = new EmailService();
    }

    /**
     * Show login form
     */
    public function showLogin()
    {
        if ($this->authService->isAuthenticated()) {
            return $this->redirectBasedOnRole();
        }

        return $this->view('auth/login', [
            'title' => 'Login - JobSpace',
            'csrf_token' => Session::generateCsrfToken()
        ]);
    }

    /**
     * Handle login
     */
    public function login()
    {
        $request = new Request();
        
        // Validate CSRF token
        if (!Session::validateCsrfToken($request->post('csrf_token'))) {
            return Response::json(['error' => 'Invalid CSRF token'], 403);
        }

        $validator = new Validator($request->all());
        $validator->rules([
            'email' => 'required|email',
            'password' => 'required|min:6',
            'remember' => 'boolean'
        ]);

        if (!$validator->validate()) {
            return Response::json(['errors' => $validator->getErrors()], 422);
        }

        $email = $request->post('email');
        $password = $request->post('password');
        $remember = $request->post('remember', false);

        // Check if account is locked
        if ($this->userModel->isAccountLocked($email)) {
            return Response::json(['error' => 'Account is temporarily locked. Please try again later.'], 423);
        }

        $user = $this->userModel->findByEmail($email);
        
        if (!$user || !$this->userModel->verifyPassword($password, $user['password'])) {
            $this->userModel->updateLoginAttempts($email, false);
            
            // Lock account after 5 failed attempts
            $attempts = $this->getLoginAttempts($email);
            if ($attempts >= 5) {
                $this->userModel->lockAccount($email, 15); // 15 minutes
            }
            
            return Response::json(['error' => 'Invalid credentials'], 401);
        }

        // Check if email is verified
        if (!$user['email_verified_at']) {
            return Response::json([
                'error' => 'Please verify your email address first',
                'redirect' => '/verify-email-notice'
            ], 403);
        }

        // Successful login
        $this->userModel->updateLoginAttempts($email, true);
        $this->authService->login($user, $remember);

        return Response::json([
            'success' => true,
            'message' => 'Login successful',
            'redirect' => $this->getRedirectUrl($user['role'])
        ]);
    }

    /**
     * Show registration form
     */
    public function showRegister()
    {
        if ($this->authService->isAuthenticated()) {
            return $this->redirectBasedOnRole();
        }

        return $this->view('auth/register', [
            'title' => 'Register - JobSpace',
            'csrf_token' => Session::generateCsrfToken(),
            'show_admin_role' => !$this->userModel->adminExists()
        ]);
    }

    /**
     * Handle registration step 1
     */
    public function registerStep1()
    {
        $request = new Request();
        
        // Validate CSRF token
        if (!Session::validateCsrfToken($request->post('csrf_token'))) {
            return Response::json(['error' => 'Invalid CSRF token'], 403);
        }

        $validator = new Validator($request->all());
        $validator->rules([
            'first_name' => 'required|min:2|max:50',
            'last_name' => 'required|min:2|max:50',
            'email' => 'required|email|max:100',
            'phone' => 'required|min:10|max:20',
            'date_of_birth' => 'required|date',
            'gender' => 'required|in:male,female,other',
            'password' => 'required|min:8|max:255',
            'confirm_password' => 'required|same:password'
        ]);

        if (!$validator->validate()) {
            return Response::json(['errors' => $validator->getErrors()], 422);
        }

        // Check if email already exists
        if ($this->userModel->emailExists($request->post('email'))) {
            return Response::json(['errors' => ['email' => ['Email already exists']]], 422);
        }

        // Store step 1 data in session
        Session::set('registration_step1', $request->except(['confirm_password', 'csrf_token']));

        return Response::json([
            'success' => true,
            'message' => 'Step 1 completed',
            'next_step' => 2
        ]);
    }

    /**
     * Handle registration step 2
     */
    public function registerStep2()
    {
        $request = new Request();
        
        // Validate CSRF token
        if (!Session::validateCsrfToken($request->post('csrf_token'))) {
            return Response::json(['error' => 'Invalid CSRF token'], 403);
        }

        // Get step 1 data
        $step1Data = Session::get('registration_step1');
        if (!$step1Data) {
            return Response::json(['error' => 'Please complete step 1 first'], 400);
        }

        $validator = new Validator($request->all());
        $validator->rules([
            'username' => 'required|min:3|max:50|alpha_dash',
            'address' => 'required|min:10|max:500',
            'role' => 'required|in:user,business,creator,admin',
            'referral_code' => 'nullable|max:20',
            'terms_accepted' => 'required|accepted'
        ]);

        if (!$validator->validate()) {
            return Response::json(['errors' => $validator->getErrors()], 422);
        }

        // Check if username already exists
        if ($this->userModel->usernameExists($request->post('username'))) {
            return Response::json(['errors' => ['username' => ['Username already exists']]], 422);
        }

        // Validate role selection
        $role = $request->post('role');
        if ($role === 'admin' && $this->userModel->adminExists()) {
            return Response::json(['errors' => ['role' => ['Admin role is not available']]], 422);
        }

        // Validate referral code if provided
        $referralCode = $request->post('referral_code');
        $referredBy = null;
        if ($referralCode) {
            $referrer = $this->userModel->findByReferralCode($referralCode);
            if (!$referrer) {
                return Response::json(['errors' => ['referral_code' => ['Invalid referral code']]], 422);
            }
            $referredBy = $referrer['id'];
        }

        // Merge step 1 and step 2 data
        $userData = array_merge($step1Data, [
            'username' => $request->post('username'),
            'address' => $request->post('address'),
            'role' => $role,
            'referred_by' => $referredBy,
            'status' => 'pending'
        ]);

        try {
            // Create user
            $userId = $this->userModel->create($userData);
            
            if ($userId) {
                // Create email verification
                $verification = $this->emailVerification->createVerification($userId);
                
                // Send verification email
                $this->emailService->sendVerificationEmail($userData['email'], $verification);
                
                // Clear registration session data
                Session::forget('registration_step1');
                
                return Response::json([
                    'success' => true,
                    'message' => 'Registration successful! Please check your email for verification.',
                    'redirect' => '/verify-email-notice'
                ]);
            }
        } catch (\Exception $e) {
            return Response::json(['error' => 'Registration failed. Please try again.'], 500);
        }

        return Response::json(['error' => 'Registration failed'], 500);
    }

    /**
     * Check email availability
     */
    public function checkEmail()
    {
        $request = new Request();
        $email = $request->post('email');
        
        if (!$email || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return Response::json(['available' => false, 'message' => 'Invalid email format']);
        }
        
        $exists = $this->userModel->emailExists($email);
        
        return Response::json([
            'available' => !$exists,
            'message' => $exists ? 'Email already exists' : 'Email is available'
        ]);
    }

    /**
     * Check username availability
     */
    public function checkUsername()
    {
        $request = new Request();
        $username = $request->post('username');
        
        if (!$username || strlen($username) < 3) {
            return Response::json(['available' => false, 'message' => 'Username must be at least 3 characters']);
        }
        
        $exists = $this->userModel->usernameExists($username);
        
        return Response::json([
            'available' => !$exists,
            'message' => $exists ? 'Username already exists' : 'Username is available'
        ]);
    }

    /**
     * Get redirect URL based on role
     */
    private function getRedirectUrl(string $role): string
    {
        switch ($role) {
            case 'admin':
            case 'creator':
            case 'business':
                return '/dashboard';
            case 'user':
            default:
                return '/feed';
        }
    }

    /**
     * Redirect based on current user role
     */
    private function redirectBasedOnRole()
    {
        $user = $this->authService->user();
        if ($user) {
            return redirect($this->getRedirectUrl($user['role']));
        }
        return redirect('/');
    }

    /**
     * Handle logout
     */
    public function logout()
    {
        $request = new Request();

        // Validate CSRF token
        if (!Session::validateCsrfToken($request->post('csrf_token'))) {
            return Response::json(['error' => 'Invalid CSRF token'], 403);
        }

        if ($this->authService->logout()) {
            if ($request->isAjax()) {
                return Response::json([
                    'success' => true,
                    'message' => 'Logged out successfully',
                    'redirect' => '/'
                ]);
            }

            return redirect('/');
        }

        return Response::json(['error' => 'Logout failed'], 500);
    }

    /**
     * Get login attempts for email
     */
    private function getLoginAttempts(string $email): int
    {
        $user = $this->userModel->findByEmail($email);
        return $user ? $user['login_attempts'] : 0;
    }
}
