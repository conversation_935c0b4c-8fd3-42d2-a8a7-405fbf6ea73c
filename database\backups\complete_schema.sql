-- =====================================================
-- JOBSPACE DATABASE SCHEMA
-- High-Performance Schema for 50K+ Concurrent Users
-- Optimized for MariaDB with InnoDB Engine
-- =====================================================

SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- =====================================================
-- 1. CORE SYSTEM TABLES
-- =====================================================

-- System Configuration Table
CREATE TABLE `system_config` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `config_key` varchar(100) NOT NULL,
    `config_value` longtext,
    `config_type` enum('string','integer','boolean','json','array') DEFAULT 'string',
    `is_public` tinyint(1) DEFAULT 0,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `config_key` (`config_key`),
    KEY `idx_config_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Cache Management Table
CREATE TABLE `cache_entries` (
    `cache_key` varchar(255) NOT NULL,
    `cache_value` longtext,
    `cache_tags` json,
    `expires_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`cache_key`),
    KEY `idx_cache_expires` (`expires_at`),
    KEY `idx_cache_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Session Management Table
CREATE TABLE `user_sessions` (
    `id` varchar(128) NOT NULL,
    `user_id` bigint(20) UNSIGNED DEFAULT NULL,
    `ip_address` varchar(45),
    `user_agent` text,
    `payload` longtext,
    `last_activity` int(11),
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_sessions_user_id` (`user_id`),
    KEY `idx_sessions_last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 2. USER MANAGEMENT TABLES
-- =====================================================

-- Main Users Table (Partitioned by user_id for scalability)
CREATE TABLE `users` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL,
    `email` varchar(100) NOT NULL,
    `email_verified_at` timestamp NULL DEFAULT NULL,
    `password` varchar(255) NOT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `phone_verified_at` timestamp NULL DEFAULT NULL,
    `first_name` varchar(50) DEFAULT NULL,
    `last_name` varchar(50) DEFAULT NULL,
    `avatar` varchar(255) DEFAULT NULL,
    `cover_image` varchar(255) DEFAULT NULL,
    `bio` text,
    `date_of_birth` date DEFAULT NULL,
    `gender` enum('male','female','other') DEFAULT NULL,
    `country` varchar(2) DEFAULT NULL,
    `city` varchar(100) DEFAULT NULL,
    `timezone` varchar(50) DEFAULT 'UTC',
    `language` varchar(5) DEFAULT 'en',
    `role` enum('admin','creator','user') DEFAULT 'user',
    `status` enum('active','inactive','suspended','banned') DEFAULT 'active',
    `is_verified` tinyint(1) DEFAULT 0,
    `is_premium` tinyint(1) DEFAULT 0,
    `premium_expires_at` timestamp NULL DEFAULT NULL,
    `last_login_at` timestamp NULL DEFAULT NULL,
    `last_login_ip` varchar(45) DEFAULT NULL,
    `login_count` int(11) DEFAULT 0,
    `referral_code` varchar(20) DEFAULT NULL,
    `referred_by` bigint(20) UNSIGNED DEFAULT NULL,
    `two_factor_enabled` tinyint(1) DEFAULT 0,
    `two_factor_secret` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `username` (`username`),
    UNIQUE KEY `email` (`email`),
    UNIQUE KEY `referral_code` (`referral_code`),
    KEY `idx_users_role` (`role`),
    KEY `idx_users_status` (`status`),
    KEY `idx_users_verified` (`is_verified`),
    KEY `idx_users_premium` (`is_premium`),
    KEY `idx_users_referred_by` (`referred_by`),
    KEY `idx_users_created_at` (`created_at`),
    KEY `idx_users_last_login` (`last_login_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Profiles Extended Information
CREATE TABLE `user_profiles` (
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `website` varchar(255) DEFAULT NULL,
    `facebook` varchar(255) DEFAULT NULL,
    `twitter` varchar(255) DEFAULT NULL,
    `linkedin` varchar(255) DEFAULT NULL,
    `instagram` varchar(255) DEFAULT NULL,
    `youtube` varchar(255) DEFAULT NULL,
    `github` varchar(255) DEFAULT NULL,
    `skills` json DEFAULT NULL,
    `interests` json DEFAULT NULL,
    `education` json DEFAULT NULL,
    `experience` json DEFAULT NULL,
    `achievements` json DEFAULT NULL,
    `certifications` json DEFAULT NULL,
    `portfolio_items` json DEFAULT NULL,
    `privacy_settings` json DEFAULT NULL,
    `notification_settings` json DEFAULT NULL,
    `theme_preferences` json DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`user_id`),
    CONSTRAINT `fk_user_profiles_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Statistics Table
CREATE TABLE `user_statistics` (
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `total_points` bigint(20) DEFAULT 0,
    `total_coins` bigint(20) DEFAULT 0,
    `level` int(11) DEFAULT 1,
    `experience_points` bigint(20) DEFAULT 0,
    `quiz_attempts` int(11) DEFAULT 0,
    `quiz_wins` int(11) DEFAULT 0,
    `quiz_points` bigint(20) DEFAULT 0,
    `social_posts` int(11) DEFAULT 0,
    `social_likes_received` int(11) DEFAULT 0,
    `social_comments_made` int(11) DEFAULT 0,
    `freelance_jobs_completed` int(11) DEFAULT 0,
    `freelance_earnings` decimal(15,2) DEFAULT 0.00,
    `ecommerce_purchases` int(11) DEFAULT 0,
    `ecommerce_spent` decimal(15,2) DEFAULT 0.00,
    `referrals_count` int(11) DEFAULT 0,
    `login_streak` int(11) DEFAULT 0,
    `last_activity_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`user_id`),
    KEY `idx_user_stats_points` (`total_points`),
    KEY `idx_user_stats_level` (`level`),
    KEY `idx_user_stats_activity` (`last_activity_at`),
    CONSTRAINT `fk_user_statistics_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Roles and Permissions
CREATE TABLE `user_roles` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    `display_name` varchar(100) NOT NULL,
    `description` text,
    `permissions` json,
    `is_active` tinyint(1) DEFAULT 1,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `name` (`name`),
    KEY `idx_roles_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Role Assignments
CREATE TABLE `user_role_assignments` (
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `role_id` bigint(20) UNSIGNED NOT NULL,
    `assigned_by` bigint(20) UNSIGNED DEFAULT NULL,
    `assigned_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `expires_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`user_id`, `role_id`),
    KEY `idx_role_assignments_role` (`role_id`),
    KEY `idx_role_assignments_assigned_by` (`assigned_by`),
    KEY `idx_role_assignments_expires` (`expires_at`),
    CONSTRAINT `fk_role_assignments_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_role_assignments_role_id` FOREIGN KEY (`role_id`) REFERENCES `user_roles` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_role_assignments_assigned_by` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Password Reset Tokens
CREATE TABLE `password_reset_tokens` (
    `email` varchar(100) NOT NULL,
    `token` varchar(255) NOT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`email`),
    KEY `idx_password_reset_token` (`token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Email Verification Tokens
CREATE TABLE `email_verification_tokens` (
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `token` varchar(255) NOT NULL,
    `expires_at` timestamp NOT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`user_id`),
    UNIQUE KEY `token` (`token`),
    KEY `idx_email_verification_expires` (`expires_at`),
    CONSTRAINT `fk_email_verification_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Activity Logs
CREATE TABLE `user_activity_logs` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) UNSIGNED DEFAULT NULL,
    `action` varchar(100) NOT NULL,
    `description` text,
    `ip_address` varchar(45),
    `user_agent` text,
    `metadata` json,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_activity_logs_user_id` (`user_id`),
    KEY `idx_activity_logs_action` (`action`),
    KEY `idx_activity_logs_created_at` (`created_at`),
    CONSTRAINT `fk_activity_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Devices
CREATE TABLE `user_devices` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `device_name` varchar(100),
    `device_type` enum('desktop','mobile','tablet') DEFAULT 'desktop',
    `browser` varchar(50),
    `os` varchar(50),
    `ip_address` varchar(45),
    `is_trusted` tinyint(1) DEFAULT 0,
    `last_used_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_devices_user_id` (`user_id`),
    KEY `idx_user_devices_trusted` (`is_trusted`),
    KEY `idx_user_devices_last_used` (`last_used_at`),
    CONSTRAINT `fk_user_devices_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 3. QUIZ SYSTEM TABLES
-- =====================================================

-- Quiz Categories
CREATE TABLE `quiz_categories` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `slug` varchar(100) NOT NULL,
    `description` text,
    `icon` varchar(255),
    `color` varchar(7) DEFAULT '#007bff',
    `parent_id` bigint(20) UNSIGNED DEFAULT NULL,
    `sort_order` int(11) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `slug` (`slug`),
    KEY `idx_quiz_categories_parent` (`parent_id`),
    KEY `idx_quiz_categories_active` (`is_active`),
    KEY `idx_quiz_categories_sort` (`sort_order`),
    CONSTRAINT `fk_quiz_categories_parent` FOREIGN KEY (`parent_id`) REFERENCES `quiz_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Quizzes
CREATE TABLE `quizzes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL,
    `slug` varchar(255) NOT NULL,
    `description` text,
    `thumbnail` varchar(255),
    `category_id` bigint(20) UNSIGNED NOT NULL,
    `creator_id` bigint(20) UNSIGNED NOT NULL,
    `difficulty_level` enum('beginner','intermediate','advanced','expert') DEFAULT 'beginner',
    `quiz_type` enum('practice','competitive','timed','unlimited') DEFAULT 'practice',
    `total_questions` int(11) DEFAULT 0,
    `time_limit` int(11) DEFAULT NULL, -- in minutes
    `pass_percentage` decimal(5,2) DEFAULT 60.00,
    `max_attempts` int(11) DEFAULT NULL,
    `points_per_question` int(11) DEFAULT 1,
    `entry_fee` decimal(10,2) DEFAULT 0.00,
    `prize_pool` decimal(15,2) DEFAULT 0.00,
    `is_public` tinyint(1) DEFAULT 1,
    `is_featured` tinyint(1) DEFAULT 0,
    `is_premium` tinyint(1) DEFAULT 0,
    `status` enum('draft','published','archived','suspended') DEFAULT 'draft',
    `starts_at` timestamp NULL DEFAULT NULL,
    `ends_at` timestamp NULL DEFAULT NULL,
    `published_at` timestamp NULL DEFAULT NULL,
    `total_attempts` int(11) DEFAULT 0,
    `total_participants` int(11) DEFAULT 0,
    `average_score` decimal(5,2) DEFAULT 0.00,
    `tags` json DEFAULT NULL,
    `settings` json DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `slug` (`slug`),
    KEY `idx_quizzes_category` (`category_id`),
    KEY `idx_quizzes_creator` (`creator_id`),
    KEY `idx_quizzes_difficulty` (`difficulty_level`),
    KEY `idx_quizzes_type` (`quiz_type`),
    KEY `idx_quizzes_status` (`status`),
    KEY `idx_quizzes_featured` (`is_featured`),
    KEY `idx_quizzes_premium` (`is_premium`),
    KEY `idx_quizzes_public` (`is_public`),
    KEY `idx_quizzes_published` (`published_at`),
    KEY `idx_quizzes_starts` (`starts_at`),
    KEY `idx_quizzes_ends` (`ends_at`),
    CONSTRAINT `fk_quizzes_category` FOREIGN KEY (`category_id`) REFERENCES `quiz_categories` (`id`) ON DELETE RESTRICT,
    CONSTRAINT `fk_quizzes_creator` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Quiz Questions
CREATE TABLE `quiz_questions` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `quiz_id` bigint(20) UNSIGNED NOT NULL,
    `question_text` text NOT NULL,
    `question_type` enum('multiple_choice','true_false','short_answer','essay','matching','ordering') DEFAULT 'multiple_choice',
    `question_image` varchar(255) DEFAULT NULL,
    `question_audio` varchar(255) DEFAULT NULL,
    `question_video` varchar(255) DEFAULT NULL,
    `points` int(11) DEFAULT 1,
    `time_limit` int(11) DEFAULT NULL, -- in seconds
    `difficulty` enum('easy','medium','hard') DEFAULT 'medium',
    `explanation` text,
    `sort_order` int(11) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_quiz_questions_quiz` (`quiz_id`),
    KEY `idx_quiz_questions_type` (`question_type`),
    KEY `idx_quiz_questions_difficulty` (`difficulty`),
    KEY `idx_quiz_questions_active` (`is_active`),
    KEY `idx_quiz_questions_sort` (`sort_order`),
    CONSTRAINT `fk_quiz_questions_quiz` FOREIGN KEY (`quiz_id`) REFERENCES `quizzes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Quiz Question Options
CREATE TABLE `quiz_question_options` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `question_id` bigint(20) UNSIGNED NOT NULL,
    `option_text` text NOT NULL,
    `option_image` varchar(255) DEFAULT NULL,
    `is_correct` tinyint(1) DEFAULT 0,
    `sort_order` int(11) DEFAULT 0,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_quiz_options_question` (`question_id`),
    KEY `idx_quiz_options_correct` (`is_correct`),
    KEY `idx_quiz_options_sort` (`sort_order`),
    CONSTRAINT `fk_quiz_options_question` FOREIGN KEY (`question_id`) REFERENCES `quiz_questions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Quiz Attempts
CREATE TABLE `quiz_attempts` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `quiz_id` bigint(20) UNSIGNED NOT NULL,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `attempt_number` int(11) DEFAULT 1,
    `started_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `completed_at` timestamp NULL DEFAULT NULL,
    `time_taken` int(11) DEFAULT NULL, -- in seconds
    `total_questions` int(11) DEFAULT 0,
    `correct_answers` int(11) DEFAULT 0,
    `wrong_answers` int(11) DEFAULT 0,
    `skipped_answers` int(11) DEFAULT 0,
    `score_percentage` decimal(5,2) DEFAULT 0.00,
    `points_earned` int(11) DEFAULT 0,
    `is_passed` tinyint(1) DEFAULT 0,
    `status` enum('in_progress','completed','abandoned','disqualified') DEFAULT 'in_progress',
    `ip_address` varchar(45),
    `user_agent` text,
    `answers` json DEFAULT NULL,
    `metadata` json DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_quiz_attempts_quiz` (`quiz_id`),
    KEY `idx_quiz_attempts_user` (`user_id`),
    KEY `idx_quiz_attempts_status` (`status`),
    KEY `idx_quiz_attempts_completed` (`completed_at`),
    KEY `idx_quiz_attempts_score` (`score_percentage`),
    CONSTRAINT `fk_quiz_attempts_quiz` FOREIGN KEY (`quiz_id`) REFERENCES `quizzes` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_quiz_attempts_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Quiz Leaderboards
CREATE TABLE `quiz_leaderboards` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `quiz_id` bigint(20) UNSIGNED NOT NULL,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `best_score` decimal(5,2) DEFAULT 0.00,
    `best_time` int(11) DEFAULT NULL,
    `total_attempts` int(11) DEFAULT 0,
    `rank_position` int(11) DEFAULT 0,
    `points_earned` int(11) DEFAULT 0,
    `last_attempt_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `quiz_user_unique` (`quiz_id`, `user_id`),
    KEY `idx_leaderboards_quiz` (`quiz_id`),
    KEY `idx_leaderboards_user` (`user_id`),
    KEY `idx_leaderboards_score` (`best_score`),
    KEY `idx_leaderboards_rank` (`rank_position`),
    CONSTRAINT `fk_leaderboards_quiz` FOREIGN KEY (`quiz_id`) REFERENCES `quizzes` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_leaderboards_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 4. SOCIAL MEDIA TABLES
-- =====================================================

-- Posts
CREATE TABLE `posts` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `content` text,
    `post_type` enum('text','image','video','link','poll','quiz_share','product_share') DEFAULT 'text',
    `media_urls` json DEFAULT NULL,
    `link_url` varchar(500) DEFAULT NULL,
    `link_title` varchar(255) DEFAULT NULL,
    `link_description` text DEFAULT NULL,
    `link_image` varchar(255) DEFAULT NULL,
    `privacy` enum('public','friends','private') DEFAULT 'public',
    `is_featured` tinyint(1) DEFAULT 0,
    `is_sponsored` tinyint(1) DEFAULT 0,
    `location` varchar(255) DEFAULT NULL,
    `tags` json DEFAULT NULL,
    `mentions` json DEFAULT NULL,
    `hashtags` json DEFAULT NULL,
    `likes_count` int(11) DEFAULT 0,
    `comments_count` int(11) DEFAULT 0,
    `shares_count` int(11) DEFAULT 0,
    `views_count` int(11) DEFAULT 0,
    `engagement_score` decimal(8,2) DEFAULT 0.00,
    `status` enum('active','hidden','reported','deleted') DEFAULT 'active',
    `scheduled_at` timestamp NULL DEFAULT NULL,
    `published_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_posts_user` (`user_id`),
    KEY `idx_posts_type` (`post_type`),
    KEY `idx_posts_privacy` (`privacy`),
    KEY `idx_posts_status` (`status`),
    KEY `idx_posts_featured` (`is_featured`),
    KEY `idx_posts_sponsored` (`is_sponsored`),
    KEY `idx_posts_published` (`published_at`),
    KEY `idx_posts_engagement` (`engagement_score`),
    CONSTRAINT `fk_posts_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Post Likes
CREATE TABLE `post_likes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `post_id` bigint(20) UNSIGNED NOT NULL,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `reaction_type` enum('like','love','laugh','angry','sad','wow') DEFAULT 'like',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `post_user_unique` (`post_id`, `user_id`),
    KEY `idx_post_likes_post` (`post_id`),
    KEY `idx_post_likes_user` (`user_id`),
    KEY `idx_post_likes_reaction` (`reaction_type`),
    CONSTRAINT `fk_post_likes_post` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_post_likes_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Post Comments
CREATE TABLE `post_comments` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `post_id` bigint(20) UNSIGNED NOT NULL,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `parent_id` bigint(20) UNSIGNED DEFAULT NULL,
    `content` text NOT NULL,
    `media_url` varchar(255) DEFAULT NULL,
    `likes_count` int(11) DEFAULT 0,
    `replies_count` int(11) DEFAULT 0,
    `is_pinned` tinyint(1) DEFAULT 0,
    `status` enum('active','hidden','reported','deleted') DEFAULT 'active',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_post_comments_post` (`post_id`),
    KEY `idx_post_comments_user` (`user_id`),
    KEY `idx_post_comments_parent` (`parent_id`),
    KEY `idx_post_comments_status` (`status`),
    KEY `idx_post_comments_pinned` (`is_pinned`),
    CONSTRAINT `fk_post_comments_post` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_post_comments_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_post_comments_parent` FOREIGN KEY (`parent_id`) REFERENCES `post_comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Post Shares
CREATE TABLE `post_shares` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `post_id` bigint(20) UNSIGNED NOT NULL,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `share_type` enum('repost','quote','external') DEFAULT 'repost',
    `share_content` text DEFAULT NULL,
    `platform` varchar(50) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_post_shares_post` (`post_id`),
    KEY `idx_post_shares_user` (`user_id`),
    KEY `idx_post_shares_type` (`share_type`),
    CONSTRAINT `fk_post_shares_post` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_post_shares_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Follows
CREATE TABLE `user_follows` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `follower_id` bigint(20) UNSIGNED NOT NULL,
    `following_id` bigint(20) UNSIGNED NOT NULL,
    `status` enum('pending','accepted','blocked') DEFAULT 'accepted',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `follower_following_unique` (`follower_id`, `following_id`),
    KEY `idx_user_follows_follower` (`follower_id`),
    KEY `idx_user_follows_following` (`following_id`),
    KEY `idx_user_follows_status` (`status`),
    CONSTRAINT `fk_user_follows_follower` FOREIGN KEY (`follower_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_user_follows_following` FOREIGN KEY (`following_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 5. E-COMMERCE TABLES
-- =====================================================

-- Product Categories
CREATE TABLE `product_categories` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `slug` varchar(100) NOT NULL,
    `description` text,
    `image` varchar(255),
    `parent_id` bigint(20) UNSIGNED DEFAULT NULL,
    `sort_order` int(11) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `slug` (`slug`),
    KEY `idx_product_categories_parent` (`parent_id`),
    KEY `idx_product_categories_active` (`is_active`),
    KEY `idx_product_categories_sort` (`sort_order`),
    CONSTRAINT `fk_product_categories_parent` FOREIGN KEY (`parent_id`) REFERENCES `product_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Products
CREATE TABLE `products` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `slug` varchar(255) NOT NULL,
    `description` text,
    `short_description` varchar(500),
    `sku` varchar(100),
    `category_id` bigint(20) UNSIGNED NOT NULL,
    `seller_id` bigint(20) UNSIGNED NOT NULL,
    `brand` varchar(100),
    `price` decimal(10,2) NOT NULL,
    `sale_price` decimal(10,2) DEFAULT NULL,
    `cost_price` decimal(10,2) DEFAULT NULL,
    `stock_quantity` int(11) DEFAULT 0,
    `min_stock_level` int(11) DEFAULT 0,
    `weight` decimal(8,2) DEFAULT NULL,
    `dimensions` json DEFAULT NULL,
    `images` json DEFAULT NULL,
    `gallery` json DEFAULT NULL,
    `attributes` json DEFAULT NULL,
    `variations` json DEFAULT NULL,
    `tags` json DEFAULT NULL,
    `seo_title` varchar(255),
    `seo_description` varchar(500),
    `is_digital` tinyint(1) DEFAULT 0,
    `is_featured` tinyint(1) DEFAULT 0,
    `is_virtual` tinyint(1) DEFAULT 0,
    `requires_shipping` tinyint(1) DEFAULT 1,
    `status` enum('draft','published','out_of_stock','discontinued') DEFAULT 'draft',
    `visibility` enum('public','private','catalog','search') DEFAULT 'public',
    `total_sales` int(11) DEFAULT 0,
    `average_rating` decimal(3,2) DEFAULT 0.00,
    `review_count` int(11) DEFAULT 0,
    `view_count` int(11) DEFAULT 0,
    `published_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `slug` (`slug`),
    UNIQUE KEY `sku` (`sku`),
    KEY `idx_products_category` (`category_id`),
    KEY `idx_products_seller` (`seller_id`),
    KEY `idx_products_status` (`status`),
    KEY `idx_products_visibility` (`visibility`),
    KEY `idx_products_featured` (`is_featured`),
    KEY `idx_products_price` (`price`),
    KEY `idx_products_rating` (`average_rating`),
    KEY `idx_products_sales` (`total_sales`),
    CONSTRAINT `fk_products_category` FOREIGN KEY (`category_id`) REFERENCES `product_categories` (`id`) ON DELETE RESTRICT,
    CONSTRAINT `fk_products_seller` FOREIGN KEY (`seller_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Shopping Cart
CREATE TABLE `shopping_cart` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) UNSIGNED DEFAULT NULL,
    `session_id` varchar(128) DEFAULT NULL,
    `product_id` bigint(20) UNSIGNED NOT NULL,
    `quantity` int(11) NOT NULL DEFAULT 1,
    `price` decimal(10,2) NOT NULL,
    `product_options` json DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_cart_user` (`user_id`),
    KEY `idx_cart_session` (`session_id`),
    KEY `idx_cart_product` (`product_id`),
    CONSTRAINT `fk_cart_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_cart_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Orders
CREATE TABLE `orders` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_number` varchar(50) NOT NULL,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `status` enum('pending','processing','shipped','delivered','cancelled','refunded') DEFAULT 'pending',
    `payment_status` enum('pending','paid','failed','refunded','partially_refunded') DEFAULT 'pending',
    `subtotal` decimal(10,2) NOT NULL,
    `tax_amount` decimal(10,2) DEFAULT 0.00,
    `shipping_amount` decimal(10,2) DEFAULT 0.00,
    `discount_amount` decimal(10,2) DEFAULT 0.00,
    `total_amount` decimal(10,2) NOT NULL,
    `currency` varchar(3) DEFAULT 'USD',
    `payment_method` varchar(50),
    `payment_reference` varchar(255),
    `billing_address` json,
    `shipping_address` json,
    `notes` text,
    `tracking_number` varchar(100),
    `shipped_at` timestamp NULL DEFAULT NULL,
    `delivered_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `order_number` (`order_number`),
    KEY `idx_orders_user` (`user_id`),
    KEY `idx_orders_status` (`status`),
    KEY `idx_orders_payment_status` (`payment_status`),
    KEY `idx_orders_created` (`created_at`),
    CONSTRAINT `fk_orders_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Order Items
CREATE TABLE `order_items` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_id` bigint(20) UNSIGNED NOT NULL,
    `product_id` bigint(20) UNSIGNED NOT NULL,
    `product_name` varchar(255) NOT NULL,
    `product_sku` varchar(100),
    `quantity` int(11) NOT NULL,
    `price` decimal(10,2) NOT NULL,
    `total` decimal(10,2) NOT NULL,
    `product_options` json DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_order_items_order` (`order_id`),
    KEY `idx_order_items_product` (`product_id`),
    CONSTRAINT `fk_order_items_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_order_items_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 6. FREELANCING MARKETPLACE TABLES
-- =====================================================

-- Job Categories
CREATE TABLE `job_categories` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `slug` varchar(100) NOT NULL,
    `description` text,
    `icon` varchar(255),
    `parent_id` bigint(20) UNSIGNED DEFAULT NULL,
    `sort_order` int(11) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `slug` (`slug`),
    KEY `idx_job_categories_parent` (`parent_id`),
    KEY `idx_job_categories_active` (`is_active`),
    KEY `idx_job_categories_sort` (`sort_order`),
    CONSTRAINT `fk_job_categories_parent` FOREIGN KEY (`parent_id`) REFERENCES `job_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Jobs
CREATE TABLE `jobs` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL,
    `slug` varchar(255) NOT NULL,
    `description` text NOT NULL,
    `category_id` bigint(20) UNSIGNED NOT NULL,
    `employer_id` bigint(20) UNSIGNED NOT NULL,
    `job_type` enum('fixed','hourly','contest') DEFAULT 'fixed',
    `experience_level` enum('entry','intermediate','expert') DEFAULT 'intermediate',
    `budget_type` enum('fixed','hourly','negotiable') DEFAULT 'fixed',
    `budget_min` decimal(10,2) DEFAULT NULL,
    `budget_max` decimal(10,2) DEFAULT NULL,
    `hourly_rate_min` decimal(8,2) DEFAULT NULL,
    `hourly_rate_max` decimal(8,2) DEFAULT NULL,
    `estimated_duration` varchar(50),
    `location_type` enum('remote','onsite','hybrid') DEFAULT 'remote',
    `location` varchar(255),
    `required_skills` json,
    `preferred_skills` json,
    `attachments` json DEFAULT NULL,
    `questions` json DEFAULT NULL,
    `status` enum('draft','published','in_progress','completed','cancelled','paused') DEFAULT 'draft',
    `visibility` enum('public','private','invited_only') DEFAULT 'public',
    `is_featured` tinyint(1) DEFAULT 0,
    `is_urgent` tinyint(1) DEFAULT 0,
    `proposals_count` int(11) DEFAULT 0,
    `max_proposals` int(11) DEFAULT NULL,
    `deadline` timestamp NULL DEFAULT NULL,
    `published_at` timestamp NULL DEFAULT NULL,
    `started_at` timestamp NULL DEFAULT NULL,
    `completed_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `slug` (`slug`),
    KEY `idx_jobs_category` (`category_id`),
    KEY `idx_jobs_employer` (`employer_id`),
    KEY `idx_jobs_type` (`job_type`),
    KEY `idx_jobs_status` (`status`),
    KEY `idx_jobs_featured` (`is_featured`),
    KEY `idx_jobs_published` (`published_at`),
    KEY `idx_jobs_deadline` (`deadline`),
    CONSTRAINT `fk_jobs_category` FOREIGN KEY (`category_id`) REFERENCES `job_categories` (`id`) ON DELETE RESTRICT,
    CONSTRAINT `fk_jobs_employer` FOREIGN KEY (`employer_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Job Proposals
CREATE TABLE `job_proposals` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `job_id` bigint(20) UNSIGNED NOT NULL,
    `freelancer_id` bigint(20) UNSIGNED NOT NULL,
    `cover_letter` text NOT NULL,
    `proposed_budget` decimal(10,2),
    `proposed_duration` varchar(50),
    `hourly_rate` decimal(8,2) DEFAULT NULL,
    `attachments` json DEFAULT NULL,
    `questions_answers` json DEFAULT NULL,
    `status` enum('pending','shortlisted','accepted','rejected','withdrawn') DEFAULT 'pending',
    `is_featured` tinyint(1) DEFAULT 0,
    `submitted_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `reviewed_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `job_freelancer_unique` (`job_id`, `freelancer_id`),
    KEY `idx_proposals_job` (`job_id`),
    KEY `idx_proposals_freelancer` (`freelancer_id`),
    KEY `idx_proposals_status` (`status`),
    KEY `idx_proposals_featured` (`is_featured`),
    CONSTRAINT `fk_proposals_job` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_proposals_freelancer` FOREIGN KEY (`freelancer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Job Contracts
CREATE TABLE `job_contracts` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `job_id` bigint(20) UNSIGNED NOT NULL,
    `proposal_id` bigint(20) UNSIGNED NOT NULL,
    `employer_id` bigint(20) UNSIGNED NOT NULL,
    `freelancer_id` bigint(20) UNSIGNED NOT NULL,
    `contract_type` enum('fixed','hourly') DEFAULT 'fixed',
    `total_amount` decimal(10,2),
    `hourly_rate` decimal(8,2) DEFAULT NULL,
    `estimated_hours` int(11) DEFAULT NULL,
    `start_date` date,
    `end_date` date DEFAULT NULL,
    `terms` text,
    `milestones` json DEFAULT NULL,
    `status` enum('active','paused','completed','cancelled','disputed') DEFAULT 'active',
    `progress_percentage` decimal(5,2) DEFAULT 0.00,
    `total_paid` decimal(10,2) DEFAULT 0.00,
    `total_hours_logged` decimal(8,2) DEFAULT 0.00,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_contracts_job` (`job_id`),
    KEY `idx_contracts_proposal` (`proposal_id`),
    KEY `idx_contracts_employer` (`employer_id`),
    KEY `idx_contracts_freelancer` (`freelancer_id`),
    KEY `idx_contracts_status` (`status`),
    CONSTRAINT `fk_contracts_job` FOREIGN KEY (`job_id`) REFERENCES `jobs` (`id`) ON DELETE RESTRICT,
    CONSTRAINT `fk_contracts_proposal` FOREIGN KEY (`proposal_id`) REFERENCES `job_proposals` (`id`) ON DELETE RESTRICT,
    CONSTRAINT `fk_contracts_employer` FOREIGN KEY (`employer_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT,
    CONSTRAINT `fk_contracts_freelancer` FOREIGN KEY (`freelancer_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 7. WALLET & PAYMENT SYSTEM TABLES
-- =====================================================

-- User Wallets
CREATE TABLE `user_wallets` (
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `balance` decimal(15,2) DEFAULT 0.00,
    `pending_balance` decimal(15,2) DEFAULT 0.00,
    `total_earned` decimal(15,2) DEFAULT 0.00,
    `total_spent` decimal(15,2) DEFAULT 0.00,
    `total_withdrawn` decimal(15,2) DEFAULT 0.00,
    `currency` varchar(3) DEFAULT 'USD',
    `is_active` tinyint(1) DEFAULT 1,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`user_id`),
    KEY `idx_wallets_balance` (`balance`),
    KEY `idx_wallets_active` (`is_active`),
    CONSTRAINT `fk_wallets_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Wallet Transactions
CREATE TABLE `wallet_transactions` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `transaction_type` enum('credit','debit') NOT NULL,
    `amount` decimal(15,2) NOT NULL,
    `currency` varchar(3) DEFAULT 'USD',
    `source_type` enum('deposit','withdrawal','quiz_reward','freelance_payment','ecommerce_purchase','referral_bonus','admin_adjustment') NOT NULL,
    `source_id` bigint(20) UNSIGNED DEFAULT NULL,
    `description` varchar(255),
    `reference_number` varchar(100),
    `status` enum('pending','completed','failed','cancelled') DEFAULT 'pending',
    `payment_method` varchar(50),
    `payment_gateway` varchar(50),
    `gateway_transaction_id` varchar(255),
    `gateway_response` json DEFAULT NULL,
    `processed_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_wallet_transactions_user` (`user_id`),
    KEY `idx_wallet_transactions_type` (`transaction_type`),
    KEY `idx_wallet_transactions_source` (`source_type`),
    KEY `idx_wallet_transactions_status` (`status`),
    KEY `idx_wallet_transactions_reference` (`reference_number`),
    KEY `idx_wallet_transactions_created` (`created_at`),
    CONSTRAINT `fk_wallet_transactions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Payment Methods
CREATE TABLE `user_payment_methods` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `method_type` enum('bank_account','paypal','stripe','mobile_money','crypto') NOT NULL,
    `method_name` varchar(100),
    `account_details` json NOT NULL,
    `is_verified` tinyint(1) DEFAULT 0,
    `is_default` tinyint(1) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_payment_methods_user` (`user_id`),
    KEY `idx_payment_methods_type` (`method_type`),
    KEY `idx_payment_methods_verified` (`is_verified`),
    KEY `idx_payment_methods_default` (`is_default`),
    CONSTRAINT `fk_payment_methods_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 8. NOTIFICATION SYSTEM TABLES
-- =====================================================

-- Notifications
CREATE TABLE `notifications` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `type` varchar(100) NOT NULL,
    `title` varchar(255) NOT NULL,
    `message` text NOT NULL,
    `action_url` varchar(500) DEFAULT NULL,
    `action_text` varchar(100) DEFAULT NULL,
    `icon` varchar(100) DEFAULT NULL,
    `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
    `category` enum('system','quiz','social','ecommerce','freelance','payment','security') DEFAULT 'system',
    `data` json DEFAULT NULL,
    `is_read` tinyint(1) DEFAULT 0,
    `read_at` timestamp NULL DEFAULT NULL,
    `expires_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_notifications_user` (`user_id`),
    KEY `idx_notifications_type` (`type`),
    KEY `idx_notifications_read` (`is_read`),
    KEY `idx_notifications_priority` (`priority`),
    KEY `idx_notifications_category` (`category`),
    KEY `idx_notifications_created` (`created_at`),
    KEY `idx_notifications_expires` (`expires_at`),
    CONSTRAINT `fk_notifications_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Email Queue
CREATE TABLE `email_queue` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `to_email` varchar(255) NOT NULL,
    `to_name` varchar(255) DEFAULT NULL,
    `subject` varchar(255) NOT NULL,
    `body` longtext NOT NULL,
    `template` varchar(100) DEFAULT NULL,
    `template_data` json DEFAULT NULL,
    `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
    `status` enum('pending','sent','failed','cancelled') DEFAULT 'pending',
    `attempts` int(11) DEFAULT 0,
    `max_attempts` int(11) DEFAULT 3,
    `error_message` text DEFAULT NULL,
    `scheduled_at` timestamp NULL DEFAULT NULL,
    `sent_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_email_queue_status` (`status`),
    KEY `idx_email_queue_priority` (`priority`),
    KEY `idx_email_queue_scheduled` (`scheduled_at`),
    KEY `idx_email_queue_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 9. FEED SYSTEM TABLES
-- =====================================================

-- Feed Items (Aggregated content from all modules)
CREATE TABLE `feed_items` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `content_type` enum('post','quiz','job','product','achievement','system') NOT NULL,
    `content_id` bigint(20) UNSIGNED NOT NULL,
    `title` varchar(255),
    `description` text,
    `image_url` varchar(255),
    `action_url` varchar(500),
    `priority_score` decimal(8,2) DEFAULT 0.00,
    `engagement_score` decimal(8,2) DEFAULT 0.00,
    `is_promoted` tinyint(1) DEFAULT 0,
    `is_visible` tinyint(1) DEFAULT 1,
    `published_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `expires_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_feed_items_user` (`user_id`),
    KEY `idx_feed_items_type` (`content_type`),
    KEY `idx_feed_items_content` (`content_id`),
    KEY `idx_feed_items_priority` (`priority_score`),
    KEY `idx_feed_items_engagement` (`engagement_score`),
    KEY `idx_feed_items_promoted` (`is_promoted`),
    KEY `idx_feed_items_visible` (`is_visible`),
    KEY `idx_feed_items_published` (`published_at`),
    CONSTRAINT `fk_feed_items_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User Feed Preferences
CREATE TABLE `user_feed_preferences` (
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `show_quiz_content` tinyint(1) DEFAULT 1,
    `show_social_content` tinyint(1) DEFAULT 1,
    `show_job_content` tinyint(1) DEFAULT 1,
    `show_product_content` tinyint(1) DEFAULT 1,
    `show_achievement_content` tinyint(1) DEFAULT 1,
    `show_promoted_content` tinyint(1) DEFAULT 1,
    `preferred_categories` json DEFAULT NULL,
    `blocked_users` json DEFAULT NULL,
    `blocked_keywords` json DEFAULT NULL,
    `feed_algorithm` enum('chronological','relevance','engagement') DEFAULT 'relevance',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`user_id`),
    CONSTRAINT `fk_feed_preferences_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- 10. ADDITIONAL SYSTEM TABLES
-- =====================================================

-- File Uploads
CREATE TABLE `file_uploads` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) UNSIGNED DEFAULT NULL,
    `original_name` varchar(255) NOT NULL,
    `file_name` varchar(255) NOT NULL,
    `file_path` varchar(500) NOT NULL,
    `file_size` bigint(20) NOT NULL,
    `mime_type` varchar(100) NOT NULL,
    `file_type` enum('image','video','audio','document','archive','other') DEFAULT 'other',
    `upload_context` varchar(100) DEFAULT NULL,
    `is_public` tinyint(1) DEFAULT 0,
    `download_count` int(11) DEFAULT 0,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_file_uploads_user` (`user_id`),
    KEY `idx_file_uploads_type` (`file_type`),
    KEY `idx_file_uploads_context` (`upload_context`),
    KEY `idx_file_uploads_public` (`is_public`),
    CONSTRAINT `fk_file_uploads_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System Logs
CREATE TABLE `system_logs` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `level` enum('emergency','alert','critical','error','warning','notice','info','debug') NOT NULL,
    `message` text NOT NULL,
    `context` json DEFAULT NULL,
    `user_id` bigint(20) UNSIGNED DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `url` varchar(500) DEFAULT NULL,
    `method` varchar(10) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_system_logs_level` (`level`),
    KEY `idx_system_logs_user` (`user_id`),
    KEY `idx_system_logs_created` (`created_at`),
    CONSTRAINT `fk_system_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Rate Limiting
CREATE TABLE `rate_limits` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `identifier` varchar(255) NOT NULL,
    `action` varchar(100) NOT NULL,
    `attempts` int(11) DEFAULT 1,
    `reset_time` timestamp NOT NULL,
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `identifier_action_unique` (`identifier`, `action`),
    KEY `idx_rate_limits_reset` (`reset_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =====================================================
-- INDEXES FOR PERFORMANCE OPTIMIZATION
-- =====================================================

-- Composite indexes for common queries
CREATE INDEX `idx_users_role_status_created` ON `users` (`role`, `status`, `created_at`);
CREATE INDEX `idx_posts_user_published_status` ON `posts` (`user_id`, `published_at`, `status`);
CREATE INDEX `idx_quizzes_category_status_published` ON `quizzes` (`category_id`, `status`, `published_at`);
CREATE INDEX `idx_jobs_category_status_published` ON `jobs` (`category_id`, `status`, `published_at`);
CREATE INDEX `idx_products_category_status_price` ON `products` (`category_id`, `status`, `price`);
CREATE INDEX `idx_notifications_user_read_created` ON `notifications` (`user_id`, `is_read`, `created_at`);
CREATE INDEX `idx_wallet_transactions_user_type_created` ON `wallet_transactions` (`user_id`, `transaction_type`, `created_at`);

-- =====================================================
-- PARTITIONING FOR LARGE TABLES
-- =====================================================

-- Partition user_activity_logs by month
ALTER TABLE `user_activity_logs`
PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    PARTITION p202404 VALUES LESS THAN (202405),
    PARTITION p202405 VALUES LESS THAN (202406),
    PARTITION p202406 VALUES LESS THAN (202407),
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    PARTITION p202409 VALUES LESS THAN (202410),
    PARTITION p202410 VALUES LESS THAN (202411),
    PARTITION p202411 VALUES LESS THAN (202412),
    PARTITION p202412 VALUES LESS THAN (202501),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- =====================================================
-- INITIAL DATA SEEDING
-- =====================================================

-- Insert default system configuration
INSERT INTO `system_config` (`config_key`, `config_value`, `config_type`, `is_public`) VALUES
('site_name', 'JobSpace', 'string', 1),
('site_description', 'Ultimate Platform for Quiz, Social, E-commerce & Freelancing', 'string', 1),
('site_url', 'https://jobspace.com', 'string', 1),
('default_currency', 'USD', 'string', 1),
('default_language', 'en', 'string', 1),
('default_timezone', 'UTC', 'string', 1),
('registration_enabled', '1', 'boolean', 1),
('email_verification_required', '1', 'boolean', 0),
('max_file_upload_size', '10485760', 'integer', 0),
('cache_enabled', '1', 'boolean', 0),
('maintenance_mode', '0', 'boolean', 1);

-- Insert default user roles
INSERT INTO `user_roles` (`name`, `display_name`, `description`, `permissions`) VALUES
('admin', 'Administrator', 'Full system access', '["*"]'),
('creator', 'Creator', 'Can create content', '["create_quiz", "create_job", "create_product", "create_post"]'),
('user', 'User', 'Basic user access', '["view_content", "participate_quiz", "apply_job", "purchase_product"]');

-- Insert default categories
INSERT INTO `quiz_categories` (`name`, `slug`, `description`, `icon`, `color`) VALUES
('General Knowledge', 'general-knowledge', 'Test your general knowledge', 'brain', '#007bff'),
('Technology', 'technology', 'Tech-related quizzes', 'laptop', '#28a745'),
('Science', 'science', 'Science and research quizzes', 'flask', '#17a2b8'),
('Business', 'business', 'Business and entrepreneurship', 'briefcase', '#ffc107'),
('Entertainment', 'entertainment', 'Movies, music, and fun', 'film', '#e83e8c');

INSERT INTO `job_categories` (`name`, `slug`, `description`, `icon`) VALUES
('Web Development', 'web-development', 'Frontend and backend development', 'code'),
('Mobile Development', 'mobile-development', 'iOS and Android development', 'mobile'),
('Design', 'design', 'UI/UX and graphic design', 'palette'),
('Writing', 'writing', 'Content writing and copywriting', 'edit'),
('Marketing', 'marketing', 'Digital marketing and SEO', 'megaphone');

INSERT INTO `product_categories` (`name`, `slug`, `description`, `image`) VALUES
('Electronics', 'electronics', 'Electronic devices and gadgets', NULL),
('Books', 'books', 'Physical and digital books', NULL),
('Clothing', 'clothing', 'Fashion and apparel', NULL),
('Home & Garden', 'home-garden', 'Home improvement and gardening', NULL),
('Sports', 'sports', 'Sports equipment and accessories', NULL);

-- =====================================================
-- FOREIGN KEY CONSTRAINTS RESTORATION
-- =====================================================

SET FOREIGN_KEY_CHECKS = 1;
COMMIT;
